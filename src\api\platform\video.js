import request from '@/utils/request'


// 查询视频合成
export function listMusetalk(query) {
  return request({
    url: '/platform/video/list',
    method: 'get',
    params: query
  })
}
//创建视频合成 M版
export function createMusetalk(data) {
  return request({
    url: '/platform/video/add',
    method: 'post',
    data: data
  })
}
//根据任务结果动态调整任务状态  M版
export function genStatus(id) {
  return request({
    url: `/platform/video/genStatus/${id}`,
    method: 'get',
  })
}

// 删除合成任务
export function delMusetalk(taskIds) {
  return request({
    url: '/platform/video/' + taskIds,
    method: 'delete'
  })
}



// 创建视频合成任务 V版
export function createVideoSynthesis(data) {
  return request({
    url: '/platform/video/synthesis',
    method: 'post',
    data: data
  })
}

/**
 * 上传媒体文件(视频或音频)  V版
 */
export function uploadMedia(file, type) {
  const formData = new FormData();
  formData.append('file', file);
  return request({
    url: `/platform/video/upload/${type}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 600000
  })
}

// 获取可用的视频合成模型列表 V版
export function getAvailableModels() {
  return request({
    url: '/platform/video/models',
    method: 'get'
  })
}

// 查询视频合成任务状态 V版
export function getTaskStatus(taskNo) {
  return request({
    url: '/platform/video/statusTaskNo/'+taskNo,
    method: 'get'
  })
}

// 创建视频合成任务 H版
export function createHeygem(data) {
  return request({
    url: '/platform/video/createHeygem',
    method: 'post',
    data: data
  })
}

/**
 * 数字人对话合成（支持M版和H版）
 * @param {Object} data - DialogueSynthesisRequest 请求参数
 * @returns {Promise} 返回 Promise，解析为响应数据
 */
export function dialogueSynthesis(data) {
  return request({
    url: '/platform/video/dialogueSynthesis',
    method: 'post',
    data: data,
    timeout: 600000
  })
}

/**
 * 检查对话组任务完成状态
 * @param {String} dialogueGroupId - 对话组ID
 * @returns {Promise} 返回 Promise，解析为响应数据
 */
export function checkDialogueGroupStatus(dialogueGroupId) {
  return request({
    url: '/platform/video/checkDialogueGroupStatus',
    method: 'get',
    params: { dialogueGroupId }
  })
}

/**
 * 根据对话组ID进行云剪辑合成（基于模板）
 * @param {Object} params - 云剪辑请求参数
 * @param {String} params.dialogueGroupId - 对话组ID
 * @param {String} params.templateId - 模板ID
 * @param {String} params.title - 视频标题（可选）
 * @param {String} params.description - 视频描述（可选）
 * @returns {Promise} 返回 Promise，解析为响应数据
 */
export function dialogueVideoClipByTemplate(params) {
  // 构建URL参数
  const urlParams = new URLSearchParams()
  urlParams.append('dialogueGroupId', params.dialogueGroupId)
  urlParams.append('templateId', params.templateId)
  if (params.title) {
    urlParams.append('title', params.title)
  }
  if (params.description) {
    urlParams.append('description', params.description)
  }

  return request({
    url: '/platform/video/dialogueVideoClipByTemplate',
    method: 'post',
    data: urlParams.toString(),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    timeout: 600000
  })
}