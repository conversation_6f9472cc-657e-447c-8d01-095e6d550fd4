import request from '@/utils/request'

/**
 * 数字人对话合成（支持M版和H版）
 * @param {Object} data - DialogueSynthesisRequest 请求参数
 * @returns {Promise} 返回 Promise，解析为响应数据
 */
export function dialogueSynthesis(data) {
  return request({
    url: '/platform/video/dialogueSynthesis',
    method: 'post',
    data: data,
    timeout: 600000
  })
}

/**
 * 检查对话组任务完成状态
 * @param {String} dialogueGroupId - 对话组ID
 * @returns {Promise} 返回 Promise，解析为响应数据
 */
export function checkDialogueGroupStatus(dialogueGroupId) {
  return request({
    url: '/platform/video/checkDialogueGroupStatus',
    method: 'get',
    params: { dialogueGroupId }
  })
}

/**
 * 智能对话视频剪辑（一键剪辑，自动选择模板）
 * @param {Object} params - 云剪辑请求参数
 * @param {String} params.dialogueGroupId - 对话组ID
 * @param {String} params.title - 视频标题
 * @param {String} params.description - 视频描述（可选）
 * @returns {Promise} 返回 Promise，解析为响应数据
 */
export function autoDialogueClip(params) {
  // 构建URL参数
  const urlParams = new URLSearchParams()
  urlParams.append('dialogueGroupId', params.dialogueGroupId)
  urlParams.append('title', params.title)
  if (params.description) {
    urlParams.append('description', params.description)
  }

  return request({
    url: '/platform/video/autoDialogueClip',
    method: 'post',
    data: urlParams.toString(),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    timeout: 600000
  })
}

