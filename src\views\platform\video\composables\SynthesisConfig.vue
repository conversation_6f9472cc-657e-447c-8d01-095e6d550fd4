<template>
  <div class="synthesis-step">
    <div class="panel-header">
      <div class="header-info">
        <h3 class="panel-title">
          <el-icon class="panel-icon"><VideoCamera /></el-icon>
          视频合成配置
        </h3>
        <p class="panel-desc">选择合成版本并配置相关参数，然后开始生成对话视频</p>
      </div>
    </div>

    <div class="synthesis-content">
      <!-- 版本选择 -->
      <div class="version-section">
        <h4 class="section-title">选择合成版本</h4>
        <div class="version-grid">
          <div 
            v-for="version in versions" 
            :key="version.value"
            class="version-card"
            :class="{ active: synthesisConfig.version === version.value }"
            @click="selectVersion(version.value)"
          >
            <div class="version-icon">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <h5 class="version-name">{{ version.name }}</h5>
            <p class="version-desc">{{ version.description }}</p>
            <div class="version-features">
              <span v-for="feature in version.features" :key="feature" class="feature-tag">
                {{ feature }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 版本特定配置 -->
      <div v-if="synthesisConfig.version" class="config-section">
        <h4 class="section-title">{{ getVersionName(synthesisConfig.version) }} 配置</h4>
        
        <!-- M版配置 -->
        <div v-if="synthesisConfig.version === 'M'" class="config-panel">
          <div class="config-item">
            <label class="config-label">阈值设置 (bboxShiftValue)</label>
            <div class="slider-container">
              <el-slider
                v-model="synthesisConfig.bboxShiftValue"
                :min="-7"
                :max="7"
                :step="1"
                show-stops
                show-input
                class="config-slider"
                @change="updateConfig"
              />
            </div>
            <p class="config-tip">调整数字人面部区域的偏移量，范围：-7 到 +7</p>
          </div>
        </div>

        <!-- V版配置 -->
        <div v-if="synthesisConfig.version === 'V'" class="config-panel">
          <div class="config-item">
            <label class="config-label">选择AI模型</label>
            <el-select 
              v-model="synthesisConfig.model" 
              placeholder="请选择AI模型" 
              class="model-select"
              size="large"
              @change="updateConfig"
            >
              <el-option
                v-for="model in availableModels"
                :key="model.modelCode"
                :label="model.modelName"
                :value="model.modelCode"
                :disabled="model.modelStatus !== 1"
              >
                <div class="model-option">
                  <span class="model-name">{{ model.modelName }}</span>
                  <span class="model-status" :class="{ available: model.modelStatus === 1 }">
                    {{ model.modelStatus === 1 ? '可用' : '不可用' }}
                  </span>
                </div>
              </el-option>
            </el-select>
            <p class="config-tip">选择用于视频合成的AI模型，不同模型效果可能有所差异</p>
          </div>
        </div>

        <!-- H版配置 -->
        <div v-if="synthesisConfig.version === 'H'" class="config-panel">
          <div class="config-info">
            <el-icon class="info-icon"><InfoFilled /></el-icon>
            <div class="info-content">
              <h5>H版 - 高清画质</h5>
              <p>H版使用默认的高清配置，无需额外设置。将为您提供最佳的画质效果。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 合成预览 -->
      <div class="preview-section">
        <h4 class="section-title">合成预览</h4>
        <div class="preview-grid">
          <div class="preview-item">
            <div class="preview-label">参与数字人</div>
            <div class="preview-value">{{ humanCount }} 个</div>
          </div>
          <div class="preview-item">
            <div class="preview-label">对话内容</div>
            <div class="preview-value">{{ dialogueCount }} 条</div>
          </div>
          <div class="preview-item">
            <div class="preview-label">合成版本</div>
            <div class="preview-value">{{ getVersionName(synthesisConfig.version) }}</div>
          </div>
          <div class="preview-item">
            <div class="preview-label">预计时长</div>
            <div class="preview-value">{{ estimatedDuration }} 秒</div>
          </div>
        </div>
      </div>

      <!-- 合成控制 -->
      <div class="synthesis-control">
        <div class="control-buttons">
          <el-button type="primary" size="large" @click="startSynthesis" :loading="synthesisTask.status === 'running'" :disabled="!canStart" class="synthesis-btn">
            <el-icon><VideoPlay /></el-icon>
            {{ synthesisTask.status === 'running' ? '合成中...' : '开始合成' }}
          </el-button>
        </div>

        <div v-if="synthesisTask.message" class="synthesis-message">
          <p>{{ synthesisTask.message }}</p>
        </div>
      </div>

      <!-- 云剪辑合成区域 -->
      <div v-if="dialogueGroupId" class="clip-section">
        <h4 class="section-title">云剪辑合成</h4>

        <!-- 任务状态检查 -->
        <div class="status-check">
          <div class="status-info">
            <div class="status-item">
              <span class="status-label">对话组ID:</span>
              <span class="status-value">{{ dialogueGroupId }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">任务状态:</span>
              <span class="status-value" :class="getStatusClass(groupStatus.status)">
                {{ getStatusText(groupStatus.status) }}
              </span>
            </div>
            <div class="status-item">
              <span class="status-label">完成进度:</span>
              <span class="status-value">{{ groupStatus.completedTasks || 0 }}/{{ groupStatus.totalTasks || 0 }}</span>
            </div>
          </div>

          <div class="status-actions">
            <el-button @click="checkGroupStatus" :loading="statusChecking" class="check-btn">
              <el-icon><Refresh /></el-icon>检查状态
            </el-button>
            <el-button
              type="success"
              @click="startVideoClip"
              :loading="clipTask.status === 'running'"
              :disabled="!canStartClip"
              class="clip-btn"
            >
              <el-icon><VideoCamera /></el-icon>
              {{ clipTask.status === 'running' ? '合成中...' : '开始云剪辑' }}
            </el-button>
          </div>
        </div>

        <!-- 任务详情 -->
        <div v-if="groupStatus.taskDetails && groupStatus.taskDetails.length > 0" class="task-details">
          <h5 class="details-title">任务详情</h5>
          <div class="task-list">
            <div
              v-for="task in groupStatus.taskDetails"
              :key="task.taskId"
              class="task-item"
              :class="getTaskStatusClass(task.status)"
            >
              <div class="task-order">{{ task.order }}</div>
              <div class="task-info">
                <div class="task-speaker">{{ getTaskSpeakerName(task) }}</div>
                <div class="task-text">{{ task.text || '无文本内容' }}</div>
              </div>
              <div class="task-status">
                <span class="status-badge">{{ task.statusText || '未知状态' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 云剪辑配置 -->
        <div v-if="groupStatus.canClip" class="clip-config">
          <h5 class="config-title">云剪辑配置</h5>
          <div class="config-description">
            <p>云剪辑将把所有数字人对话视频按顺序合成为一个完整的对话视频。后端会自动选择合适的模板。</p>
          </div>
          <div class="config-form">
            <div class="form-row">
              <label class="form-label">视频标题</label>
              <el-input
                v-model="clipConfig.title"
                placeholder="请输入视频标题"
                maxlength="50"
                show-word-limit
                class="form-input"
              />
            </div>
            <div class="form-row">
              <label class="form-label">视频描述</label>
              <el-input
                v-model="clipConfig.description"
                type="textarea"
                :rows="3"
                placeholder="请输入视频描述（可选）"
                maxlength="200"
                show-word-limit
                class="form-textarea"
              />
            </div>
          </div>
        </div>

        <!-- 云剪辑结果 -->
        <div v-if="clipTask.result" class="clip-result">
          <h5 class="result-title">云剪辑结果</h5>
          <div class="result-info">
            <div class="result-item">
              <span class="result-label">项目ID:</span>
              <span class="result-value">{{ clipTask.result.projectId }}</span>
            </div>
            <div class="result-item">
              <span class="result-label">任务ID:</span>
              <span class="result-value">{{ clipTask.result.jobId }}</span>
            </div>
            <div v-if="clipTask.result.message" class="result-item">
              <span class="result-label">状态:</span>
              <span class="result-value">{{ clipTask.result.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { VideoCamera, VideoPlay, InfoFilled, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { dialogueSynthesis, checkDialogueGroupStatus, dialogueVideoClipByTemplate } from '@/api/platform/video'
import { listWymodel } from '@/api/platform/model'

const props = defineProps({
  digitalHumans: {
    type: Array,
    default: () => []
  },
  dialogueContent: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['synthesis-complete'])

// 组件内部状态
const availableModels = ref([])
const synthesisConfig = reactive({
  version: 'M',
  bboxShiftValue: 0,
  model: ''
})

const synthesisTask = reactive({
  status: 'idle',
  message: ''
})

// 云剪辑相关数据
const dialogueGroupId = ref('')
const statusChecking = ref(false)
const groupStatus = reactive({
  status: '',
  message: '',
  canClip: false,
  totalTasks: 0,
  completedTasks: 0,
  failedTasks: 0,
  processingTasks: 0,
  pendingTasks: 0,
  alreadyClipped: false,
  taskDetails: []
})

const clipConfig = reactive({
  title: '',
  description: ''
})

const clipTask = reactive({
  status: 'idle',
  message: '',
  result: null
})

// 定时器
let statusCheckTimer = null

// 内置音色列表
const builtinVoices = ['zhiyuan', 'zhiyue', 'zhisha', 'zhida', 'aiqi', 'aicheng', 'aijia', 'siqi', 'sijia', 'mashu', 'yueer', 'ruoxi', 'aida', 'sicheng', 'ninger', 'xiaoyun', 'xiaogang', 'ruilin']

// 计算属性
const humanCount = computed(() => props.digitalHumans.length)
const dialogueCount = computed(() => props.dialogueContent.length)
const estimatedDuration = computed(() => {
  const avgWordsPerSecond = 3
  const totalWords = props.dialogueContent.reduce((sum, dialogue) => {
    return sum + (dialogue.text ? dialogue.text.length : 0)
  }, 0)
  return Math.max(10, Math.ceil(totalWords / avgWordsPerSecond))
})

const canStart = computed(() => {
  return synthesisConfig.version &&
         props.digitalHumans.length >= 2 &&
         props.dialogueContent.length > 0 &&
         (synthesisConfig.version !== 'V' || synthesisConfig.model) &&
         synthesisTask.status !== 'running'
})

const canStartClip = computed(() => {
  return groupStatus.canClip &&
         clipConfig.title.trim() &&
         clipTask.status !== 'running'
})

const versions = [
  {
    value: 'M',
    name: 'M版',
    description: '标准版本，支持基础对话合成',
    features: ['标准画质', '快速合成', '阈值调节']
  },
  {
    value: 'V',
    name: 'V版',
    description: '增强版本，支持AI模型选择',
    features: ['AI增强', '模型选择', '优化效果']
  },
  {
    value: 'H',
    name: 'H版',
    description: '高清版本，提供最佳画质',
    features: ['高清画质', '专业效果', '默认配置']
  }
]

// 方法
const selectVersion = (version) => {
  synthesisConfig.version = version
  if (version === 'M') {
    synthesisConfig.bboxShiftValue = 0
  } else if (version === 'V') {
    synthesisConfig.model = ''
    loadAvailableModels()
  }
}

const loadAvailableModels = async () => {
  try {
    const response = await listWymodel()
    if (response && response.data) {
      availableModels.value = response.data.filter(model => model.modelStatus === 1)
      if (availableModels.value.length > 0 && !synthesisConfig.model) {
        synthesisConfig.model = availableModels.value[0].modelCode
      }
    }
  } catch (error) {
    console.error('加载模型失败:', error)
    ElMessage.error('加载模型失败')
  }
}

// 合成前验证
const validateSynthesisData = () => {
  // 验证基本配置
  if (!synthesisConfig.version) {
    throw new Error('请选择合成版本')
  }

  if (props.digitalHumans.length < 2) {
    throw new Error('至少需要选择2个数字人')
  }

  if (props.dialogueContent.length === 0) {
    throw new Error('请添加对话内容')
  }

  // 验证V版模型配置
  if (synthesisConfig.version === 'V' && !synthesisConfig.model) {
    throw new Error('V版需要选择AI模型')
  }

  // 验证对话内容
  props.dialogueContent.forEach((dialogue, index) => {
    if (!dialogue.text || dialogue.text.trim() === '') {
      throw new Error(`第${index + 1}条对话内容不能为空`)
    }
    if (!dialogue.speaker) {
      throw new Error(`第${index + 1}条对话缺少发言人`)
    }
  })

  // 验证数字人配置
  props.digitalHumans.forEach((human, index) => {
    if (!human.name || human.name.trim() === '') {
      throw new Error(`第${index + 1}个数字人缺少名称`)
    }
    if (!human.voiceId && !human.voiceName) {
      throw new Error(`第${index + 1}个数字人缺少声音配置`)
    }
  })
}

const startSynthesis = async () => {
  if (!canStart.value) {
    ElMessage.warning('请完成所有配置后再开始合成')
    return
  }

  try {
    // 合成前验证
    validateSynthesisData()

    synthesisTask.status = 'running'
    synthesisTask.message = '正在准备合成任务...'

    // 构建合成参数
    const synthesisParams = {
      version: synthesisConfig.version,
      digitalHumans: props.digitalHumans.map((human, index) => {
        // 验证必要字段
        if (!human.avatarAddress) {
          throw new Error(`数字人${index + 1}缺少形象地址`)
        }
        if (!human.name || human.name.trim() === '') {
          throw new Error(`数字人${index + 1}缺少名称`)
        }
        if (!human.voiceName) {
          throw new Error(`数字人${index + 1}缺少声音名称`)
        }

        // 处理声音类型和ID
        let voiceType = human.voiceType || 'builtin'
        let voiceId = null

        if (voiceType === 'system') {
          voiceId = parseInt(human.voiceId)
          if (!voiceId || voiceId <= 0) {
            throw new Error(`数字人${index + 1}的系统声音ID无效`)
          }
        } else if (voiceType === 'builtin') {
          if (!builtinVoices.includes(human.voiceName)) {
            throw new Error(`数字人${index + 1}使用了不支持的内置音色: ${human.voiceName}`)
          }
        }

        // 构建规范的数字人配置
        return {
          id: human.id,
          name: human.name.trim(),
          avatarAddress: human.avatarAddress,
          avatarName: human.avatarName,
          voiceType: voiceType,
          voiceId: voiceId,
          voiceName: human.voiceName
        }
      }),
      dialogueContent: props.dialogueContent.map((dialogue, index) => {
        // 验证对话内容
        if (!dialogue.text || dialogue.text.trim() === '') {
          throw new Error(`对话${index + 1}的文本内容不能为空`)
        }

        if (!dialogue.speaker) {
          throw new Error(`对话${index + 1}缺少发言人配置`)
        }

        // 验证发言人是否存在于数字人配置中
        const humanExists = props.digitalHumans.some(human =>
          human.id === dialogue.speaker
        )

        if (!humanExists) {
          throw new Error(`对话${index + 1}的发言人配置错误: ${dialogue.speaker}`)
        }

        // 构建规范的对话数据
        return {
          id: index + 1,
          speaker: dialogue.speaker, // 格式为 "human_imageId"
          speakerName: dialogue.speakerName || `数字人${index + 1}`,
          text: dialogue.text.trim(),
          order: index + 1 // 确保顺序字段始终有值，从1开始
        }
      }),
      bboxShiftValue: synthesisConfig.version === 'M' ? synthesisConfig.bboxShiftValue : null,
      model: synthesisConfig.version === 'V' ? synthesisConfig.model : null
    }

    const response = await dialogueSynthesis(synthesisParams)

    if (response && response.data) {
      synthesisTask.status = 'success'
      synthesisTask.message = '合成任务已提交！'

      // 获取对话组ID和任务信息
      if (response.data.dialogueGroupId) {
        dialogueGroupId.value = response.data.dialogueGroupId

        // 生成默认的视频标题和描述
        const currentTime = new Date().toLocaleString('zh-CN')
        clipConfig.title = `数字人对话视频_${currentTime}`
        clipConfig.description = `包含${props.dialogueContent.length}条对话的数字人视频，参与数字人：${props.digitalHumans.length}个`

        // 显示任务创建信息
        const taskCount = response.data.videoTaskIds ? response.data.videoTaskIds.length : props.dialogueContent.length
        synthesisTask.message = `已创建${taskCount}个视频合成任务，对话组ID: ${dialogueGroupId.value}`

        // 开始定时检查状态
        startStatusCheck()
        ElMessage.success(`合成任务已提交！已创建${taskCount}个子任务，正在检查状态...`)
      } else {
        ElMessage.success('视频合成任务已提交，请到视频管理页面查看进度')
      }

      emit('synthesis-complete', response.data)
    } else {
      throw new Error('合成响应异常')
    }
  } catch (error) {
    console.error('合成失败:', error)
    synthesisTask.status = 'error'
    synthesisTask.message = '合成失败: ' + (error.message || '未知错误')
    ElMessage.error('视频合成失败: ' + (error.message || '请稍后重试'))
  }
}

const getVersionName = (version) => {
  const versionMap = {
    'M': 'M版 - 标准版',
    'V': 'V版 - 增强版',
    'H': 'H版 - 高清版'
  }
  return versionMap[version] || '未选择'
}

// 检查对话组状态
const checkGroupStatus = async () => {
  if (!dialogueGroupId.value) {
    ElMessage.warning('对话组ID为空')
    return
  }

  statusChecking.value = true
  try {
    const response = await checkDialogueGroupStatus(dialogueGroupId.value)

    if (response.code === 200 && response.data) {
      Object.assign(groupStatus, response.data)

      // 更新合成任务状态信息
      if (groupStatus.status === 'COMPLETED') {
        synthesisTask.message = `所有任务已完成！共${groupStatus.totalTasks}个任务，可以进行云剪辑合成`
        stopStatusCheck()
      } else if (groupStatus.status === 'FAILED') {
        synthesisTask.message = `有${groupStatus.failedTasks}个任务失败，无法进行云剪辑合成`
        stopStatusCheck()
      } else if (groupStatus.status === 'PROCESSING') {
        synthesisTask.message = `正在处理中...已完成${groupStatus.completedTasks}/${groupStatus.totalTasks}个任务`
      } else if (groupStatus.status === 'PENDING') {
        synthesisTask.message = `等待处理中...${groupStatus.pendingTasks}个任务待处理`
      } else if (groupStatus.status === 'NOT_FOUND') {
        synthesisTask.message = '未找到对应的任务，请稍后重试'
        stopStatusCheck()
      }
    } else {
      throw new Error(response.msg || '检查状态失败')
    }
  } catch (error) {
    console.error('检查对话组状态失败:', error)
    ElMessage.error('检查状态失败: ' + (error.message || '未知错误'))
  } finally {
    statusChecking.value = false
  }
}



// 开始云剪辑合成
const startVideoClip = async () => {
  if (!dialogueGroupId.value) {
    ElMessage.warning('对话组ID为空')
    return
  }

  if (!canStartClip.value) {
    ElMessage.warning('请完成所有配置后再开始云剪辑')
    return
  }

  clipTask.status = 'running'
  clipTask.message = '正在进行云剪辑合成...'
  clipTask.result = null

  try {
    console.log('开始云剪辑合成，参数:', {
      dialogueGroupId: dialogueGroupId.value,
      title: clipConfig.title,
      description: clipConfig.description
    })

    const response = await dialogueVideoClipByTemplate({
      dialogueGroupId: dialogueGroupId.value,
      templateId: 'auto', // 让后端自动选择模板
      title: clipConfig.title,
      description: clipConfig.description
    })

    console.log('云剪辑响应:', response)

    if (response.code === 200 && response.data) {
      handleClipSuccess(response.data, '模板云剪辑')
    } else {
      throw new Error(response.msg || '云剪辑合成失败')
    }

  } catch (error) {
    console.error('云剪辑合成失败:', error)
    clipTask.status = 'error'
    clipTask.message = '云剪辑合成失败: ' + (error.message || '未知错误')
    ElMessage.error('云剪辑合成失败: ' + (error.message || '请稍后重试'))
  }
}



// 处理云剪辑成功
const handleClipSuccess = (data, method) => {
  clipTask.status = 'success'
  clipTask.result = data

  // 构建详细的成功信息
  let successMessage = `${method}任务已提交成功！`
  if (data.projectId) {
    successMessage += `\n项目ID: ${data.projectId}`
  }
  if (data.jobId) {
    successMessage += `\n任务ID: ${data.jobId}`
  }

  clipTask.message = successMessage

  // 更新状态，标记已进行云剪辑
  groupStatus.alreadyClipped = true
  groupStatus.canClip = false
  groupStatus.message += '（已进行云剪辑合成）'

  ElMessage.success({
    message: '云剪辑合成任务已提交成功！请到视频管理页面查看合成进度',
    duration: 5000
  })
}



// 开始定时检查状态
const startStatusCheck = () => {
  // 立即检查一次
  checkGroupStatus()

  // 每30秒检查一次状态
  statusCheckTimer = setInterval(() => {
    if (groupStatus.status !== 'COMPLETED' && groupStatus.status !== 'FAILED') {
      checkGroupStatus()
    } else {
      stopStatusCheck()
    }
  }, 30000)
}

// 停止定时检查
const stopStatusCheck = () => {
  if (statusCheckTimer) {
    clearInterval(statusCheckTimer)
    statusCheckTimer = null
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 'COMPLETED':
      return 'status-completed'
    case 'FAILED':
      return 'status-failed'
    case 'PROCESSING':
      return 'status-processing'
    case 'PENDING':
      return 'status-pending'
    default:
      return 'status-unknown'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'COMPLETED':
      return '全部完成'
    case 'FAILED':
      return '有任务失败'
    case 'PROCESSING':
      return '处理中'
    case 'PENDING':
      return '等待处理'
    case 'NOT_FOUND':
      return '未找到任务'
    case 'ERROR':
      return '检查错误'
    default:
      return '未知状态'
  }
}

// 获取任务状态样式类
const getTaskStatusClass = (status) => {
  switch (status) {
    case '3':
      return 'task-completed'
    case '4':
      return 'task-failed'
    case '2':
      return 'task-processing'
    case '1':
      return 'task-pending'
    default:
      return 'task-unknown'
  }
}

// 获取任务发言人名称
const getTaskSpeakerName = (task) => {
  // 如果任务中有speakerName，直接使用
  if (task.speakerName) {
    return task.speakerName
  }

  // 如果没有speakerName，尝试根据speaker字段找到对应的数字人
  if (task.speaker && props.digitalHumans && props.digitalHumans.length > 0) {
    const human = props.digitalHumans.find(h => h.id === task.speaker)
    if (human && human.name) {
      return human.name
    }
  }

  // 如果都找不到，根据任务顺序生成默认名称
  return `数字人${task.order || '未知'}`
}

onMounted(() => {
  if (synthesisConfig.version === 'V') {
    loadAvailableModels()
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopStatusCheck()
})
</script>

<style lang="scss" scoped>
.synthesis-step {
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    border: 1px solid #e9ecef;

    .header-info {
      .panel-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin: 0 0 8px 0;

        .panel-icon {
          font-size: 28px;
          color: #667eea;
        }
      }

      .panel-desc {
        font-size: 16px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }
  }

  .synthesis-content {
    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0 0 20px 0;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '';
        width: 4px;
        height: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
      }
    }

    .version-section {
      margin-bottom: 40px;

      .version-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        margin-top: 20px;
      }

      .version-card {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 16px;
        padding: 24px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          transform: scaleX(0);
          transition: transform 0.3s ease;
        }

        &:hover {
          border-color: #667eea;
          transform: translateY(-4px);
          box-shadow: 0 8px 20px rgba(102, 126, 234, 0.2);

          &::before {
            transform: scaleX(1);
          }
        }

        &.active {
          border-color: #667eea;
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);

          &::before {
            transform: scaleX(1);
          }

          .version-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
          }
        }

        .version-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          background: #f8f9fa;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: #667eea;
          margin-bottom: 16px;
          transition: all 0.3s ease;
        }

        .version-name {
          font-size: 20px;
          font-weight: 600;
          color: #333;
          margin: 0 0 8px 0;
        }

        .version-desc {
          font-size: 14px;
          color: #666;
          margin: 0 0 16px 0;
          line-height: 1.5;
        }

        .version-features {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .feature-tag {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            color: #667eea;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            border: 1px solid rgba(102, 126, 234, 0.2);
          }
        }
      }
    }

    .config-section {
      margin-bottom: 40px;

      .config-panel {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid #e9ecef;

        .config-item {
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }

          .config-label {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
          }

          .slider-container {
            margin-bottom: 12px;

            .config-slider {
              :deep(.el-slider__runway) {
                background: #e9ecef;
              }

              :deep(.el-slider__bar) {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              }

              :deep(.el-slider__button) {
                border-color: #667eea;
              }
            }
          }

          .model-select {
            width: 100%;
            margin-bottom: 12px;

            :deep(.el-select__wrapper) {
              border-radius: 12px;
            }
          }

          .config-tip {
            font-size: 14px;
            color: #666;
            margin: 0;
            line-height: 1.5;
          }
        }

        .config-info {
          display: flex;
          align-items: flex-start;
          gap: 16px;
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
          border: 1px solid rgba(102, 126, 234, 0.2);
          border-radius: 12px;
          padding: 20px;

          .info-icon {
            font-size: 24px;
            color: #667eea;
            flex-shrink: 0;
          }

          .info-content {
            h5 {
              font-size: 16px;
              font-weight: 600;
              color: #333;
              margin: 0 0 8px 0;
            }

            p {
              font-size: 14px;
              color: #666;
              margin: 0;
              line-height: 1.5;
            }
          }
        }
      }
    }

    .preview-section {
      margin-bottom: 40px;

      .preview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-top: 20px;
      }

      .preview-item {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        transition: all 0.3s ease;

        &:hover {
          border-color: #667eea;
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
        }

        .preview-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .preview-value {
          font-size: 20px;
          font-weight: 600;
          color: #333;
        }
      }
    }

    .synthesis-control {
      margin-bottom: 40px;
      text-align: center;

      .control-buttons {
        display: flex;
        justify-content: center;
        gap: 16px;
        margin-bottom: 24px;

        .synthesis-btn {
          background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
          border: none;
          border-radius: 12px;
          padding: 16px 32px;
          font-size: 16px;
          font-weight: 600;
          transition: all 0.3s ease;

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(103, 194, 58, 0.3);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }

      .synthesis-message {
        margin-top: 20px;
        text-align: center;

        p {
          font-size: 16px;
          color: #666;
          margin: 0;
        }
      }
    }
  }

  // 云剪辑合成区域
  .clip-section {
    margin-top: 40px;
    padding: 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    border: 1px solid #e9ecef;

    .status-check {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 24px;
      padding: 20px;
      background: white;
      border-radius: 12px;
      border: 1px solid #e9ecef;

      .status-info {
        flex: 1;

        .status-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .status-label {
            font-weight: 600;
            color: #666;
            margin-right: 12px;
            min-width: 80px;
          }

          .status-value {
            font-weight: 500;
            color: #333;

            &.status-completed {
              color: #67c23a;
            }

            &.status-failed {
              color: #f56c6c;
            }

            &.status-processing {
              color: #e6a23c;
            }

            &.status-pending {
              color: #909399;
            }

            &.status-unknown {
              color: #909399;
            }
          }
        }
      }

      .status-actions {
        display: flex;
        gap: 12px;

        .check-btn {
          background: white;
          border: 2px solid #e9ecef;
          color: #666;
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover {
            border-color: #667eea;
            color: #667eea;
          }
        }

        .clip-btn {
          background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
          border: none;
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }
    }

    .task-details {
      margin-bottom: 24px;

      .details-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 16px 0;
      }

      .task-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .task-item {
        display: flex;
        align-items: center;
        padding: 16px;
        background: white;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &.task-completed {
          border-left: 4px solid #67c23a;
        }

        &.task-failed {
          border-left: 4px solid #f56c6c;
        }

        &.task-processing {
          border-left: 4px solid #e6a23c;
        }

        &.task-pending {
          border-left: 4px solid #909399;
        }

        .task-order {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 14px;
          margin-right: 16px;
          flex-shrink: 0;
        }

        .task-info {
          flex: 1;

          .task-speaker {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
          }

          .task-text {
            color: #666;
            font-size: 14px;
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 300px;
          }
        }

        .task-status {
          .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            background: #f0f0f0;
            color: #666;
          }
        }
      }
    }

    .clip-config {
      margin-bottom: 24px;
      padding: 20px;
      background: white;
      border-radius: 12px;
      border: 1px solid #e9ecef;

      .config-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 12px 0;
      }

      .config-description {
        margin-bottom: 16px;
        padding: 12px;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        border-radius: 8px;
        border-left: 4px solid #667eea;

        p {
          margin: 0;
          font-size: 14px;
          color: #666;
          line-height: 1.5;
        }
      }

      .config-form {
        .form-row {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
          }

          .form-input,
          .form-textarea {
            width: 100%;
            border-radius: 8px;
          }
        }
      }
    }

    .clip-result {
      padding: 20px;
      background: white;
      border-radius: 12px;
      border: 1px solid #e9ecef;

      .result-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 16px 0;
      }

      .result-info {
        .result-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .result-label {
            font-weight: 600;
            color: #666;
            margin-right: 12px;
            min-width: 80px;
          }

          .result-value {
            color: #333;
            font-family: monospace;
            background: #f8f9fa;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 14px;
          }
        }
      }
    }
  }

  .model-option {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .model-name {
      font-weight: 500;
    }

    .model-status {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 4px;
      background: #f56c6c;
      color: white;

      &.available {
        background: #67c23a;
      }
    }
  }
}

@media (max-width: 768px) {
  .synthesis-step {
    .clip-section {
      .status-check {
        flex-direction: column;
        gap: 16px;

        .status-actions {
          width: 100%;
          justify-content: center;

          .check-btn,
          .clip-btn {
            flex: 1;
          }
        }
      }

      .task-item {
        .task-info .task-text {
          max-width: none;
          white-space: normal;
        }
      }
    }
  }
}
</style>
