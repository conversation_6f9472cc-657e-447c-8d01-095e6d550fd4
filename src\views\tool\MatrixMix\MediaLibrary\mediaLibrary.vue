<template>
  <div class="media-library">
    <!-- 顶部标签栏 -->
    <div class="header-tabs">
      <div class="tab-item" :class="{ active: activeTab === 'video' }" @click="switchTab('video')">
        <el-icon>
          <VideoPlay />
        </el-icon>
        <span>视频库(多媒体)</span>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'audio' }" @click="switchTab('audio')">
        <el-icon>
          <Microphone />
        </el-icon>
        <span>音频库</span>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'image' }" @click="switchTab('image')">
        <el-icon>
          <Picture />
        </el-icon>
        <span>图片素材库</span>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'font' }" @click="switchTab('font')">
        <el-icon>
          <EditPen />
        </el-icon>
        <span>字体素材库</span>
      </div>
    </div>

    <!-- 内容区 -->
    <div class="content-area">
      <component 
        :is="currentComponent" 
        :key="activeTab" 
        :media-list="mediaList" 
        :current-page="currentPage"
        :page-size="pageSize" 
        :total="total" 
        :loading="loading"
        :upload-handler="handleUpload" 
        :media-response="data"
        @upload="handleUpload" 
        @refreshMediaList="loadMediaList" 
        @pageChange="handlePageChange"
        @sizeChange="handleSizeChange"
        @search="handleSearch"
        @filter="handleFilter"
      />
    </div>
    
    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination 
        v-model:current-page="currentPage" 
        v-model:page-size="pageSize" 
        :page-sizes="[10, 20, 40, 80]"
        :total="total" 
        layout="total, sizes, prev, pager, next, jumper" 
        @size-change="handleSizeChange"
        @current-change="handlePageChange" 
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoPlay, Microphone, Picture, EditPen } from '@element-plus/icons-vue'
import VideoLibrary from './components/VideoLibrary.vue'
import AudioLibrary from './components/AudioLibrary.vue'
import ImageLibrary from './components/ImageLibrary.vue'
import FontLibrary from './components/FontLibrary.vue'
import { uploadAndRegisterMedia } from '../api/media'
import { detectFileCategory } from '../utils/fileUtils'
import request from '@/utils/request'
import UploadDebugger from '../debug/uploadDebugger'

// 当前活跃的标签页
const activeTab = ref<string>('video')

// 响应式数据
const data = ref<any>(null)
const loading = ref(false)
const mediaList = ref<any[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 查询参数
const searchParams = ref<any>({
  Match: '',
  SortBy: 'utcCreate:Desc',
  PageNo: 1,
  PageSize: 20
})

// 动态组件映射
const componentMap = {
  video: VideoLibrary,
  audio: AudioLibrary,
  image: ImageLibrary,
  font: FontLibrary
}

// 当前组件
const currentComponent = computed(() => componentMap[activeTab.value as keyof typeof componentMap])

/**
 * 搜索媒资信息API
 */
async function SearchMedia(params: any): Promise<any> {
  try {
    const response = await request({
      url: '/open/openApiPost',
      method: 'post',
      data: {
        Action: 'SearchMedia',
        Version: '2020-11-09',
        ...params
      }
    });
    return response;
  } catch (error) {
    throw error;
  }
}

// 切换标签页
const switchTab = (tab: string) => {
  activeTab.value = tab
  currentPage.value = 1
  // 重置搜索条件，只保留媒体类型过滤
  searchParams.value = {
    Match: getMediaTypeFilter(tab),
    SortBy: 'utcCreate:Desc',
    PageNo: 1,
    PageSize: pageSize.value
  }
  loadMediaList()
}

// 获取媒体类型过滤条件
const getMediaTypeFilter = (tab: string) => {
  const mediaTypeMap: Record<string, string> = {
    video: "mediaType == 'video'",
    audio: "mediaType == 'audio'",
    image: "mediaType == 'image'",
    font: "mediaType == 'text'" // 字体使用text类型
  }
  return mediaTypeMap[tab] || ''
}

// 加载媒资列表
const loadMediaList = async (page?: number, customParams?: any) => {
  loading.value = true
  try {
    const currentPageNo = page || currentPage.value
    
    const params = {
      ...searchParams.value,
      PageNo: currentPageNo,
      PageSize: pageSize.value,
      ...customParams
    }

    const response = await SearchMedia(params)
    
    if (response?.data) {
      const { MediaInfoList = [], Total = 0 } = response.data
      
      // 转换数据格式以兼容现有组件
      mediaList.value = MediaInfoList.map((item: any) => ({
        MediaId: item.MediaId,
        MediaBasicInfo: {
          MediaId: item.MediaId,
          Title: item.MediaBasicInfo?.Title || item.FileInfoList?.[0]?.FileBasicInfo?.FileName || '未知',
          Description: item.MediaBasicInfo?.Description || '',
          CoverURL: item.MediaBasicInfo?.CoverURL || '',
          CreateTime: item.MediaBasicInfo?.CreateTime || '',
          MediaType: item.MediaBasicInfo?.MediaType || activeTab.value,
          Source: item.MediaBasicInfo?.Source || '',
          Status: item.MediaBasicInfo?.Status || '',
          BusinessType: item.MediaBasicInfo?.BusinessType || activeTab.value,
          Snapshots: item.MediaBasicInfo?.Snapshots || '',
          SpriteImages: item.MediaBasicInfo?.SpriteImages || '',
          Tags: item.MediaBasicInfo?.Tags || ''
        },
        FileInfoList: item.FileInfoList || [],
        CreationTime: item.MediaBasicInfo?.CreateTime || ''
      }))
      
      total.value = Total
      currentPage.value = currentPageNo
      data.value = response
    }
  } catch (error: any) {
    console.error('加载媒资列表失败:', error)
    ElMessage.error('加载媒资列表失败: ' + (error.message || '未知错误'))
    mediaList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = (searchKeyword: string) => {
  const baseFilter = getMediaTypeFilter(activeTab.value)
  
  if (searchKeyword.trim()) {
    searchParams.value.Match = `${baseFilter} and title = '${searchKeyword.trim()}'`
  } else {
    searchParams.value.Match = baseFilter
  }
  
  currentPage.value = 1
  loadMediaList(1)
}

// 处理筛选
const handleFilter = (filterParams: any) => {
  const conditions: string[] = []
  
  // 基础媒体类型条件
  const baseFilter = getMediaTypeFilter(activeTab.value)
  if (baseFilter) {
    conditions.push(baseFilter)
  }
  
  // 业务类型筛选
  if (filterParams.businessType) {
    conditions.push(`businessType == '${filterParams.businessType}'`)
  }
  
  // 来源筛选
  if (filterParams.source) {
    conditions.push(`source == '${filterParams.source}'`)
  }
  
  // 状态筛选
  if (filterParams.status) {
    conditions.push(`status == '${filterParams.status}'`)
  }
  
  // 日期范围筛选
  if (filterParams.startTime && filterParams.endTime) {
    const startTimestamp = new Date(filterParams.startTime).getTime()
    const endTimestamp = new Date(filterParams.endTime).getTime()
    conditions.push(`utcCreate >= ${startTimestamp} and utcCreate <= ${endTimestamp}`)
  }
  
  // 搜索关键词
  if (filterParams.searchKeyword) {
    conditions.push(`title = '${filterParams.searchKeyword}'`)
  }
  
  searchParams.value.Match = conditions.length > 0 ? conditions.join(' and ') : ''
  searchParams.value.SortBy = filterParams.sortBy || 'utcCreate:Desc'
  
  currentPage.value = 1
  loadMediaList(1)
}

// 分页处理
const handlePageChange = (page: number) => {
  loadMediaList(page)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  searchParams.value.PageSize = size
  currentPage.value = 1
  loadMediaList(1)
}

// 统一上传逻辑
const handleUpload = async (uploadParams: any) => {
  const uploadDebugger = UploadDebugger.getInstance()

  try {
    const category = detectFileCategory(uploadParams.file, activeTab.value)
    const currentTab = activeTab.value

    const fileAnalysis = uploadDebugger.analyzeFile(uploadParams.file)
    console.log('📋 文件分析结果:', fileAnalysis)

    if (fileAnalysis.potentialIssues.length > 0) {
      console.warn('⚠️ 检测到潜在问题:', fileAnalysis.potentialIssues)
      for (const issue of fileAnalysis.potentialIssues) {
        ElMessage.warning(`${issue.issue}: ${issue.recommendation}`)
      }
    }

    const metadata = {
      title: uploadParams.MediaMetaData?.Title || uploadParams.file.name.split('.').slice(0, -1).join('.'),
      description: uploadParams.MediaMetaData?.Description || `${category}文件: ${uploadParams.file.name}`,
      tags: uploadParams.MediaMetaData?.Tags || `${category},media`,
      businessType: uploadParams.MediaMetaData?.BusinessType || category
    }

    if (category !== currentTab) {
      const categoryNames = {
        video: '视频',
        audio: '音频',
        music: '音乐',
        image: '图片',
        font: '字体',
        text: '文本'
      }
      const detectedName = categoryNames[category as keyof typeof categoryNames] || category
      const currentName = categoryNames[currentTab as keyof typeof categoryNames] || currentTab

      ElMessage.info(`检测到${detectedName}文件，将按${detectedName}类型上传（当前在${currentName}标签页）`)
    }

    const testResult = await uploadDebugger.testUpload(uploadParams.file, category, metadata)

    if (!testResult.success) {
      throw new Error(testResult.error || '上传测试失败')
    }

    const result = testResult.result

    console.log('✅ 一体化上传成功:', result)
    console.log('📊 上传性能:', `耗时 ${testResult.duration}ms`)

    const mediaId = result?.data?.MediaId || (result as any)?.MediaId
    const requestId = (result?.data as any)?.RequestId || (result as any)?.RequestId

    if (!mediaId) {
      console.error('❌ 响应中未找到MediaId:', result)
      throw new Error('上传成功但未返回媒资ID，请检查后端响应格式')
    }

    ElMessage.success(`上传成功！文件: ${uploadParams.file.name}，媒资ID: ${mediaId}`)

    // 发出刷新事件
    const event = new CustomEvent('mediaUploaded', {
      detail: {
        mediaId: mediaId,
        category: category,
        fileName: uploadParams.file.name,
        fileInfo: {
          fileName: (result?.data as any)?.fileName || uploadParams.file.name,
          fileSize: (result?.data as any)?.fileSize || uploadParams.file.size,
          ossUrl: (result?.data as any)?.ossUrl || '',
          filePath: (result?.data as any)?.filePath || ''
        },
        mediaInfo: {
          mediaId: mediaId,
          requestId: requestId || '',
          category: (result?.data as any)?.category || category
        }
      }
    })
    window.dispatchEvent(event)
    
    // 刷新当前页面数据
    loadMediaList()
    
    return result
  } catch (error: any) {
    console.error('❌ 一体化上传错误:', error)
    const errorMessage = error.response?.data?.msg || error.message || '上传失败，请重试'
    ElMessage.error(`上传失败: ${errorMessage}`)
    throw error
  }
}

// 初始化
loadMediaList()
</script>

<style lang="scss" scoped>
.media-library {
  display: flex;
  flex-direction: column;
  height: 80vh;
  background-color: #f5f7fa;

}

.pagination-wrapper {

  display: flex;
  justify-content: center;
}

.header-tabs {
  display: flex;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .tab-item {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    border-bottom: 3px solid transparent;
    white-space: nowrap;
    position: relative;

    .el-icon {
      margin-right: 8px;
      font-size: 16px;
    }

    span {
      font-weight: 400;
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      color: #ffffff;
    }

    &.active {
      background-color: rgba(255, 255, 255, 0.15);
      color: #ffffff;
      border-bottom-color: #ffffff;
      font-weight: 500;

      span {
        font-weight: 500;
      }
    }

    &:not(:last-child) {
      border-right: 1px solid rgba(255, 255, 255, 0.1);
    }
  }
}

.content-area {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-tabs {
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;

    .tab-item {
      flex-shrink: 0;
      padding: 12px 20px;
      font-size: 13px;

      .el-icon {
        margin-right: 6px;
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 480px) {
  .header-tabs {
    .tab-item {
      padding: 10px 15px;
      font-size: 12px;

      span {
        display: none;
      }

      .el-icon {
        margin-right: 0;
        font-size: 18px;
      }
    }
  }
}
</style>
