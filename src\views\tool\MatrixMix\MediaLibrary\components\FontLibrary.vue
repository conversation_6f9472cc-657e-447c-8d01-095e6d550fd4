<template>
  <div class="font-library">
    <!-- 操作栏 -->
    <div class="operation-bar">
      <div class="left-actions">
        <el-button type="primary" @click="handleUpload">
          <el-icon><Upload /></el-icon>
          上传字体
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      <div class="right-actions">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索字体名称"
          @input="handleSearchInput"
          clearable
          style="width: 200px; margin-right: 10px;"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-select v-model="sortBy" placeholder="排序" style="width: 120px;">
          <el-option label="升序" value="utcCreate:Asc" />
          <el-option label="降序" value="utcCreate:Desc" />
        </el-select>
        <el-button type="primary" @click="handleFilter" style="margin-left: 10px;">筛选</el-button>
      </div>
    </div>

    <!-- 字体列表 -->
    <div class="font-content">
      <el-table
        v-loading="props.loading"
        :data="filteredFontList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="字体预览" width="250">
          <template #default="scope">
            <div class="font-preview" :style="getFontStyle(scope.row)">
              {{ getPreviewText(scope.row) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="字体名称">
          <template #default="scope">
            {{ scope.row.MediaBasicInfo?.Title || '未命名字体' }}
          </template>
        </el-table-column>
        <el-table-column label="文件名称">
          <template #default="scope">
            {{ scope.row.FileInfoList?.[0]?.FileBasicInfo?.FileName || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="文件大小">
          <template #default="scope">
            {{ formatFileSize(scope.row.FileInfoList?.[0]?.FileBasicInfo?.FileSize) }}
          </template>
        </el-table-column>
        <el-table-column label="格式">
          <template #default="scope">
            {{ getFontFormat(scope.row.FileInfoList?.[0]?.FileBasicInfo?.FileName) }}
          </template>
        </el-table-column>
        <el-table-column label="上传时间">
          <template #default="scope">
            {{ formatDate(scope.row.CreationTime || scope.row.MediaBasicInfo?.CreateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="text" @click="handlePreview(scope.row)">预览</el-button>
            <el-button type="text" @click="handleDownload(scope.row)">下载</el-button>
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" @click="handleDelete(scope.row)" style="color: #f56c6c">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 上传组件 -->
    <MediaUploader
      v-model:visible="uploadDialogVisible"
      media-type="font"
      @upload-success="handleUploadSuccess"
      @upload-error="handleUploadError"
    />

    <!-- 字体预览对话框 -->
    <el-dialog v-model="previewDialogVisible" title="字体预览" width="60%">
      <div v-if="previewFont" class="font-preview-dialog">
        <div class="preview-header">
          <h3>{{ previewFont.name }}</h3>
          <p>字体族：{{ previewFont.fontFamily }}</p>
        </div>
        <div class="preview-samples">
          <div 
            v-for="sample in previewSamples" 
            :key="sample.size"
            class="preview-sample"
            :style="{ 
              fontFamily: previewFont.fontFamily, 
              fontSize: sample.size + 'px' 
            }"
          >
            <span class="sample-label">{{ sample.size }}px:</span>
            {{ sample.text }}
          </div>
        </div>
        <div class="custom-preview">
          <el-input
            v-model="customPreviewText"
            placeholder="输入自定义预览文本"
            @input="updateCustomPreview"
          />
          <div 
            class="custom-text"
            :style="{ 
              fontFamily: previewFont.fontFamily,
              fontSize: '24px'
            }"
          >
            {{ customPreviewText || '自定义预览文本' }}
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="previewDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="previewFont && handleDownload(previewFont)">下载字体</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑字体信息" width="40%">
      <el-form v-if="editFont" :model="editFont" label-width="80px">
        <el-form-item label="字体名称">
          <el-input v-model="editFont.name" />
        </el-form-item>
        <el-form-item label="字体族">
          <el-input v-model="editFont.fontFamily" />
        </el-form-item>
        <el-form-item label="预览文本">
          <el-input v-model="editFont.previewText" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="editFont.description" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEdit">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Refresh, Search } from '@element-plus/icons-vue'
import MediaUploader from './add/MediaUploader.vue'
import { formatFileSize } from '../../utils/commonUtils'
import type { MediaInfo } from '../../types/media'

// 定义组件的事件和props
const emit = defineEmits<{
  upload: [uploadParams: { file: File; MediaMetaData?: any }],
  sizeChange: [size: number],
  pageChange: [current: number],
  search: [keyword: string],
  filter: [params: any],
  refreshMediaList: [page?: number]
}>()

// 定义props
const props = defineProps<{
  mediaList?: MediaInfo[],
  currentPage?: number,
  pageSize?: number,
  total?: number,
  loading?: boolean,
  uploadHandler?: (uploadParams: { file: File; MediaMetaData?: any }) => Promise<any>
}>()

// 类型定义
interface FontItem {
  id: number
  name: string
  fontFamily: string
  size: string
  format: string
  uploadTime: string
  previewText: string
  description: string
}

// 响应式数据
const selectedFonts = ref<FontItem[]>([])
const searchKeyword = ref('')
const sortBy = ref('utcCreate:Desc')

// 对话框状态
const uploadDialogVisible = ref(false)
const previewDialogVisible = ref(false)
const editDialogVisible = ref(false)

// 预览相关
const previewFont = ref<FontItem | null>(null)
const customPreviewText = ref('')
const previewSamples = [
  { size: 12, text: 'The quick brown fox jumps over the lazy dog' },
  { size: 16, text: 'The quick brown fox jumps over the lazy dog' },
  { size: 20, text: 'The quick brown fox jumps over the lazy dog' },
  { size: 24, text: 'The quick brown fox jumps over the lazy dog' },
  { size: 32, text: 'The quick brown fox' }
]

// 编辑相关
const editFont = ref<FontItem | null>(null)

// 计算属性
const filteredFontList = computed(() => {
  return props.mediaList || []
})

// 搜索处理
const handleSearchInput = () => {
  emit('search', searchKeyword.value)
}

// 筛选按钮处理
const handleFilter = () => {
  emit('filter', {
    sortBy: sortBy.value,
    searchKeyword: searchKeyword.value
  })
}

// 方法
const handleRefresh = () => {
  emit('refreshMediaList')
}

const handleSelectionChange = (selection: any[]) => {
  selectedFonts.value = selection
}

const handleUpload = () => {
  uploadDialogVisible.value = true
}

const handlePreview = (font: any) => {
  previewFont.value = {
    id: font.MediaId,
    name: font.MediaBasicInfo?.Title || '未命名字体',
    fontFamily: font.MediaBasicInfo?.Title || 'Arial',
    size: formatFileSize(font.FileInfoList?.[0]?.FileBasicInfo?.FileSize),
    format: getFontFormat(font.FileInfoList?.[0]?.FileBasicInfo?.FileName),
    uploadTime: font.CreationTime || font.MediaBasicInfo?.CreateTime,
    previewText: 'The quick brown fox',
    description: font.MediaBasicInfo?.Description || ''
  }
  customPreviewText.value = ''
  previewDialogVisible.value = true
}

const handleDownload = (font: any) => {
  const url = font.FileInfoList?.[0]?.FileBasicInfo?.FileUrl
  if (url) {
    const link = document.createElement('a')
    link.href = url
    link.download = font.MediaBasicInfo?.Title || 'font'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('字体下载成功')
  } else {
    ElMessage.error('无法获取字体下载链接')
  }
}

const handleEdit = (font: any) => {
  editFont.value = {
    id: font.MediaId,
    name: font.MediaBasicInfo?.Title || '未命名字体',
    fontFamily: font.MediaBasicInfo?.Title || 'Arial',
    size: formatFileSize(font.FileInfoList?.[0]?.FileBasicInfo?.FileSize),
    format: getFontFormat(font.FileInfoList?.[0]?.FileBasicInfo?.FileName),
    uploadTime: font.CreationTime || font.MediaBasicInfo?.CreateTime,
    previewText: 'The quick brown fox',
    description: font.MediaBasicInfo?.Description || ''
  }
  editDialogVisible.value = true
}

const handleDelete = async (font: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除字体 "${font.MediaBasicInfo?.Title || '未命名字体'}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('删除成功')
    emit('refreshMediaList')
  } catch {
    ElMessage.info('已取消删除')
  }
}

// 上传成功处理
const handleUploadSuccess = (result: any) => {
  ElMessage.success('字体上传成功！')
  uploadDialogVisible.value = false
  emit('refreshMediaList')
}

// 上传失败处理
const handleUploadError = (error: any) => {
  ElMessage.error('字体上传失败: ' + (error.message || '未知错误'))
}

// 编辑相关方法
const submitEdit = () => {
  ElMessage.success('编辑成功')
  editDialogVisible.value = false
  emit('refreshMediaList')
}

const updateCustomPreview = () => {
  // 实时更新自定义预览
}

// 工具方法
const formatDate = (dateString?: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getFontFormat = (fileName?: string) => {
  if (!fileName) return ''
  const ext = fileName.split('.').pop()?.toUpperCase()
  return ext || ''
}

const getFontStyle = (font: any) => {
  const fontUrl = font.FileInfoList?.[0]?.FileBasicInfo?.FileUrl
  if (fontUrl) {
    const fontName = `font-${font.MediaId}`
    const style = document.createElement('style')
    style.textContent = `
      @font-face {
        font-family: '${fontName}';
        src: url('${fontUrl}');
      }
    `
    document.head.appendChild(style)
    return { fontFamily: fontName }
  }
  return {}
}

const getPreviewText = (font: any) => {
  const title = font.MediaBasicInfo?.Title || '字体预览'
  return title.length > 8 ? title.substring(0, 8) + '...' : title
}

// 监听上传成功事件
const handleMediaUploaded = (event: Event) => {
  const customEvent = event as CustomEvent
  const { category } = customEvent.detail
  if (category === 'font' || category === 'text') {
    emit('refreshMediaList')
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})

onUnmounted(() => {
  window.removeEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})
</script>

<style scoped>
.font-library {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;
}

.left-actions {
  display: flex;
  gap: 12px;
}

.right-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.right-actions .el-input {
  width: 240px;
}

.font-content {
  flex: 1;
  overflow: hidden;
}

.font-preview {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
  font-size: 14px;
  min-height: 30px;
  display: flex;
  align-items: center;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 16px;
}

/* 预览对话框样式 */
.font-preview-dialog {
  padding: 20px;
}

.preview-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.preview-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #303133;
}

.preview-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.preview-samples {
  margin-bottom: 24px;
}

.preview-sample {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  line-height: 1.4;
}

.sample-label {
  display: inline-block;
  width: 60px;
  color: #909399;
  font-size: 12px;
  font-family: Arial, sans-serif;
}

.custom-preview {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.custom-preview .el-input {
  margin-bottom: 16px;
}

.custom-text {
  padding: 16px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-height: 60px;
  display: flex;
  align-items: center;
  line-height: 1.4;
}

/* 响应式 */
@media (max-width: 768px) {
  .operation-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .left-actions,
  .right-actions {
    justify-content: center;
  }
  
  .right-actions .el-input {
    width: 100%;
  }
}
</style>