/**
 * 阿里云ICE NPM包管理器 - 统一版本
 * 前端HTTP客户端，通过后端API调用ICE SDK功能
 * 同时支持云剪辑项目和模板工厂模板
 */
import request from '@/utils/request';
import { ElMessage } from 'element-plus';

export interface ICEConfig {
  timeout?: number;
}

export type EditorMode = 'project' | 'template';

/**
 * ICE NPM包管理器 - 统一管理云剪辑和模板工厂
 */
export class ICENPMManager {
  private isInitialized = false;
  private config: ICEConfig | null = null;

  /**
   * 初始化ICE管理器
   */
  async initialize(config?: ICEConfig): Promise<boolean> {
    try {
      this.config = config || {};
      this.isInitialized = true;

      return true;
    } catch (error) {
      ElMessage.error(`ICE客户端初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);
      return false;
    }
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * 初始化云剪辑项目
   */
  async initializeFromProject(projectId: string): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      // 调用云剪辑项目详情API
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'GetEditingProject',
          Version: '2020-11-09',
          ProjectId: projectId
        }
      });

      let projectData = (response as any).Project;
      // 确保ProjectId存在
      if (!projectData.ProjectId) {
        projectData.ProjectId = projectId;
      }

      // 解析Timeline数据
      let timeline = null;
      if (projectData.Timeline) {
        try {
          timeline = JSON.parse(projectData.Timeline);
        } catch (error) {
          timeline = { VideoTracks: [], AudioTracks: [], Duration: 0 };
        }
      } else {
        timeline = { VideoTracks: [], AudioTracks: [], Duration: 0 };
      }
      const result = {
        success: true,
        data: {
          ProjectId: projectData.ProjectId,
          Title: projectData.Title || '未命名项目',
          Status: projectData.Status?.toString() || 'Unknown',
          Timeline: timeline,
          CoverURL: projectData.CoverURL || '',
          ProduceMediaId: projectData.ProduceMediaId || '',
          ModifiedTime: projectData.ModifiedTime || new Date().toISOString(),
          source: 'project',
          来源: '云剪辑'
        }
      };
      return result;
    } catch (error) {
      return {
        success: false,
        data: {
          ProjectId: projectId,
          Title: '未命名项目',
          Status: 'Error',
          Timeline: { VideoTracks: [], AudioTracks: [], Duration: 0 },
          CoverURL: '',
          ProduceMediaId: '',
          ModifiedTime: new Date().toISOString(),
          source: 'project',
          来源: '云剪辑'
        },
        error: error instanceof Error ? error.message : '获取项目失败'
      };
    }
  }


  /**
   * 获取媒资信息
   */
  async getMediaInfo(mediaId: string): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }
    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'GetMediaInfo',
          Version: '2020-11-09',
          MediaId: mediaId
        }
      });
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 搜索媒资
   */
  async searchMedia(params: any): Promise<any[]> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'SearchMedia',
          Version: '2020-11-09',
          MediaType: params.mediaType || 'video',
          Keyword: params.keyword || '',
          PageSize: params.pageSize || 20,
          PageNo: params.pageNo || 1,
          SortBy: params.sortBy || 'CreationTime:Desc'
        }
      });
      return (response.data as any).MediaInfos || [];
    } catch (error) {
      throw error;
    }
  }

  /**
   * 创建编辑项目
   */
  async createProject(params: { title: string; description?: string }): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'CreateEditingProject',
          Version: '2020-11-09',
          Title: params.title,
          Description: params.description || '',
          Timeline: JSON.stringify({
            videoTracks: [],
            audioTracks: [],
            duration: 0
          })
        }
      });

      if ((response.data as any).success) {
        const projectId = (response.data as any).ProjectId;
        return {
          success: true,
          data: {
            projectId: projectId,
            title: params.title,
            timeline: { videoTracks: [], audioTracks: [], duration: 0 }
          },
          message: '项目创建成功'
        };
      }
      throw new Error((response.data as any).message || '创建项目失败');
    } catch (error: any) {
      return {
        success: false,
        message: error.message || '创建项目失败'
      };
    }
  }

  /**
   * 获取编辑项目
   */
  async getProject(projectId: string): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'GetEditingProject',
          Version: '2020-11-09',
          ProjectId: projectId
        }
      });

      if ((response.data as any).success) {
        return {
          success: true,
          data: (response.data as any).project || {
            projectId,
            title: '默认项目',
            timeline: { videoTracks: [], audioTracks: [], duration: 0 }
          },
          message: '获取项目成功'
        };
      }
      throw new Error((response.data as any).message || '获取项目失败');
    } catch (error: any) {
      return {
        success: false,
        message: error.message || '获取项目失败'
      };
    }
  }

  /**
   * 更新编辑项目
   */
  async updateProject(params: { projectId: string; title?: string; timeline?: any }): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'UpdateEditingProject',
          Version: '2020-11-09',
          ProjectId: params.projectId,
          Title: params.title,
          Timeline: params.timeline ? JSON.stringify(params.timeline) : undefined
        }
      });

      if ((response.data as any).success) {
        return {
          success: true,
          data: (response.data as any).project,
          message: '更新项目成功'
        };
      }
      throw new Error((response.data as any).message || '更新项目失败');
    } catch (error: any) {
      return {
        success: false,
        message: error.message || '更新项目失败'
      };
    }
  }

  /**
   * 获取模板
   */
  async getTemplate(templateId: string, relatedMediaidFlag: number = 1): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApi',
        method: 'get',
        params: {
          Action: 'GetTemplate',
          TemplateId: templateId,
          relatedMediaidFlag: String(relatedMediaidFlag)
        }
      });

      let template;
      if (response.data && (response.data as any).Template) {
        template = (response.data as any).Template;
      } else if ((response as any).Template) {
        template = (response as any).Template;
      } else if (response.data) {
        template = response.data as any;
      } else {
        throw new Error('响应数据格式错误，未找到模板信息');
      }

      return template;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 删除素材 - 根据模式自动选择
   */
  async deleteMaterial(id: string, mediaId: string, mediaType: string, mode: EditorMode): Promise<boolean> {
    if (mode === 'template') {
      return this.deleteTemplateMaterial(id, mediaId, mediaType);
    } else {
      return this.deleteProjectMaterial(id, mediaId, mediaType);
    }
  }

  /**
   * 删除模板素材
   */
  async deleteTemplateMaterial(templateId: string, mediaId: string, mediaType: string): Promise<boolean> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'DeleteTemplateMaterial',
          Version: '2020-11-09',
          TemplateId: templateId,
          MediaId: mediaId,
          MediaType: mediaType
        }
      });
      return (response.data as any).success || false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 删除项目素材
   */
  async deleteProjectMaterial(projectId: string, mediaId: string, mediaType: string): Promise<boolean> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'DeleteProjectMaterial',
          Version: '2020-11-09',
          ProjectId: projectId,
          MediaId: mediaId,
          MediaType: mediaType
        }
      });
      return (response.data as any).success || false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 更新模板
   */
  async updateTemplate(templateId: string, data: {
    coverUrl?: string;
    duration?: number;
    timeline?: any;
    isAuto?: boolean;
    relatedMediaids?: string;
  }): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const requestData: any = {
        Action: 'UpdateTemplate',
        Version: '2020-11-09',
        TemplateId: templateId
      };

      if (data.coverUrl !== undefined) {
        requestData.CoverURL = data.coverUrl;
      }

      if (data.timeline !== undefined) {
        requestData.Config = JSON.stringify(data.timeline);
      }

      if (data.relatedMediaids !== undefined) {
        requestData.RelatedMediaids = data.relatedMediaids;
      }

      if (data.duration !== undefined) {
        requestData.Duration = data.duration;
      }

      if (data.isAuto !== undefined) {
        requestData.IsAuto = data.isAuto;
      }

      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: requestData
      });

      if (response && (response as any).success !== false) {
        return response.data || response;
      } else {
        const errorMsg = (response as any)?.message || (response as any)?.error || '模板更新失败';
        throw new Error(errorMsg);
      }

    } catch (error: any) {
      // 根据错误类型提供更具体的错误信息
      if (error.response?.status === 404) {
        throw new Error('模板不存在或已被删除');
      } else if (error.response?.status === 403) {
        throw new Error('没有权限修改此模板');
      } else if (error.response?.status === 500) {
        throw new Error('服务器内部错误，请稍后重试');
      } else if (error.message?.includes('timeout')) {
        throw new Error('网络请求超时，请检查网络连接');
      } else {
        throw new Error(error.message || '模板保存失败，请重试');
      }
    }
  }

  /**
   * 批量获取媒资信息
   */
  async batchGetMediaInfos(params: { mediaIds: string[], additionType?: string }): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'BatchGetMediaInfos',
          Version: '2020-11-09',
          MediaIds: params.mediaIds.join(','),
          AdditionType: params.additionType || 'FileInfo'
        }
      });

      return response || { MediaInfos: [] };
    } catch (error) {
      return { MediaInfos: [] };
    }
  }
  /**
   * 通过外链URL获取媒资信息
   */
  async getMediaInfoByUrl(params: any): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'GetMediaInfo',
          Version: '2020-11-09',
          InputURL: params.InputURL,
          OutputType: params.OutputType || "cdn"
        }
      });
      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取媒体生产作业详情
   */
  async getMediaProducingJob(jobId: string): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'GetMediaProducingJob',
          Version: '2020-11-09',
          JobId: jobId
        }
      });

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 列出媒体生产作业
   */
  async listMediaProducingJobs(params: any): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'ListMediaProducingJobs',
          Version: '2020-11-09',
          ...params
        }
      });

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 提交媒体信息作业（上传媒资）
   */
  async submitMediaInfoJob(params: any): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'SubmitMediaInfoJob',
          Version: '2020-11-09',
          ...params
        }
      });

      ElMessage.success('媒体信息作业提交成功');
      return response.data;
    } catch (error) {
      ElMessage.error(`媒体信息作业提交失败: ${error instanceof Error ? error.message : '未知错误'}`);
      throw error;
    }
  }

  /**
   * 添加编辑项目素材
   */
  async addEditingProjectMaterials(projectId: string, materialMaps: any): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'AddEditingProjectMaterials',
          Version: '2020-11-09',
          ProjectId: projectId,
          MaterialMaps : materialMaps
        }
      });

      return (response as any).MediaInfos || [];
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取编辑项目素材
   */
  async getEditingProjectMaterials(projectId: string): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'GetEditingProjectMaterials',
          Version: '2020-11-09',
          ProjectId: projectId
        }
      });

      return response || [];
    } catch (error) {
      throw error;
    }
  }

  /**
   * 删除编辑项目素材
   */
  async deleteEditingProjectMaterials(projectId: string, materialType: string, materialIds: string): Promise<boolean> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'DeleteEditingProjectMaterials',
          Version: '2020-11-09',
          ProjectId: projectId,
          MaterialType: materialType,
          MaterialIds: materialIds
        }
      });

      return (response as any).code === '200';
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取所有公共媒资标签（贴纸分类）
   */
  async listAllPublicMediaTags(businessType: string, webSdkVersion: string): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'ListAllPublicMediaTags',
          Version: '2020-11-09',
          BusinessType: businessType,
          WebSdkVersion: webSdkVersion
        }
      });

      return (response as any) || [];
    } catch (error) {
      throw error;
    }
  }

  /**
   * 列出公共媒资基本信息（贴纸列表）
   */
  async listPublicMediaBasicInfos(params: any): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'ListPublicMediaBasicInfos',
          Version: '2020-11-09',
          ...params
        }
      });

      return response || {};
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取公共媒资信息 - 按照官方文档实现
   */
  async getPublicMediaInfo(params: { MediaId: string; OutputType?: string }): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'GetPublicMediaInfo',
          Version: '2020-11-09',
          MediaId: params.MediaId,
          OutputType: params.OutputType || 'cdn'
        }
      });

      return response || {};
    } catch (error) {
      throw error;
    }
  }

  /**
   * 列出媒资基础信息
   * @param StartTime 创建时间 最开始 非必填
   * @param EndTime 结束时间 结束点  他为闭区间 非必填
   * @param MediaType 媒资媒体类型 image video audio text 非必填
   * @param BusinessType 媒资业务类型 字幕 水印 片头/开场 片尾 通用 非必填
   * @param source  来源 oss vod live general 非必填
   * @param Status 资源状态 非必填
   * @param NextToken 用来表示当前调用返回读取到的位置，空代表数据已经读取完毕。 非必填
   * @param MaxResults 本次请求所返回的最大记录条数。 最大为 100 非必填
   * @param SorBy 根据创建时间来排序 默认倒序 非必填
   * @param IncludeFileBasicInfo 如果为 true，返回值中包含文件基础信息。  非必填
   * @param MediaId 媒资ID 非必填
   */
  async ListMediaBasicInfos(param: any): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }
    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'ListMediaBasicInfos',
          Version: '2020-11-09',
          ...param
        }
      });
      return response;
    } catch (error) {
      throw error;
    }
  }
  /**
   * <p>搜索媒资信息</p>
   * @param Match 过滤条件
   * @param SortBy 排序字段和排序顺序 多个使用,分割
   * @param PageNo 当前页码 默认值为1
   * @param pageSize 返回条数 默认值为10
   * @param EntitId 实体id
   * @param ScrollToken 翻页标识
   * @param SearchLibName 搜索库
   */
  async SearchMedia(params: any): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化')
    }
    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'SearchMedia',
          Version: '2020-11-09',
          ...params
        }
      });

      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 提交媒体生产作业
   */
  async submitMediaProducingJob(params: any): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'SubmitMediaProducingJob',
          Version: '2020-11-09',
          ...params
        }
      });

      ElMessage.success('媒体生产作业提交成功');
      return response.data;
    } catch (error) {
      ElMessage.error(`媒体生产作业提交失败: ${error instanceof Error ? error.message : '未知错误'}`);
      throw error;
    }
  }

  /**
   * 生成视频
   */
  async produceVideo(projectId: string, data: any): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'ProduceEditingProjectVideo',
          Version: '2020-11-09',
          ProjectId: projectId,
          ...data
        }
      });

      ElMessage.success('视频生成任务提交成功');
      return response.data;
    } catch (error) {
      ElMessage.error(`视频生成任务提交失败: ${error instanceof Error ? error.message : '未知错误'}`);
      throw error;
    }
  }

  /**
   * 提交智能字幕识别任务
   */
  async submitASRJob(mediaId: string, startTime: string, duration: string): Promise<any[]> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'SubmitASRJob',
          Version: '2020-11-09',
          MediaId: mediaId,
          StartTime: startTime,
          Duration: duration
        }
      });

      // 确保返回数组类型
      const responseData = response.data as any;
      const result = Array.isArray(responseData) ? responseData :
        (responseData?.result && Array.isArray(responseData.result)) ? responseData.result : [];

      ElMessage.success('智能字幕识别任务提交成功');
      return result;
    } catch (error) {
      ElMessage.error(`智能字幕识别任务提交失败: ${error instanceof Error ? error.message : '未知错误'}`);
      throw error;
    }
  }

  /**
   * 提交文字转语音任务
   */
  async submitAudioProduceJob(text: string, voice: string, voiceConfig?: any): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'SubmitAudioProduceJob',
          Version: '2020-11-09',
          Text: text,
          Voice: voice,
          VoiceConfig: voiceConfig || {}
        }
      });
      ElMessage.success('文字转语音任务提交成功');
      return response;
    } catch (error) {
      console.error(`文字转语音任务提交失败: ${error instanceof Error ? error.message : '未知错误'}`);
      throw error;
    }
  }

  /**
   * 创建模板
   */
  async createTemplate(params: {
    title: string;
    type?: string;
    coverUrl?: string;
    description?: string;
    config?: any;
  }): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'CreateTemplate',
          Version: '2020-11-09',
          Title: params.title,
          Type: params.type || 'Timeline',
          CoverURL: params.coverUrl || '',
          Description: params.description || '',
          Config: params.config ? JSON.stringify(params.config) : undefined
        }
      });

      ElMessage.success('模板创建成功');
      return response.data;
    } catch (error) {
      ElMessage.error(`模板创建失败: ${error instanceof Error ? error.message : '未知错误'}`);
      throw error;
    }
  }

  /**
   * 删除模板
   */
  async deleteTemplate(templateId: string): Promise<boolean> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'DeleteTemplate',
          Version: '2020-11-09',
          TemplateId: templateId
        }
      });

      ElMessage.success('模板删除成功');
      return (response as any).success !== false;
    } catch (error) {
      ElMessage.error(`模板删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
      return false;
    }
  }

  /**
   * 列出模板
   */
  async listTemplates(params: {
    pageNo?: number;
    pageSize?: number;
    status?: string;
    type?: string;
  } = {}): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApi',
        method: 'get',
        params: {
          Action: 'ListTemplates',
          PageNo: params.pageNo || 1,
          PageSize: params.pageSize || 20,
          Status: params.status || 'Available',
          Type: params.type || 'Timeline'
        }
      });

      return response.data || { TemplateList: [], TotalCount: 0 };
    } catch (error) {
      return { TemplateList: [], TotalCount: 0 };
    }
  }

  /**
   * 注册媒资
   */
  async registerMediaInfo(params: {
    mediaURL: string;
    title?: string;
    description?: string;
    tags?: string;
    category?: string;
  }): Promise<any> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'RegisterMediaInfo',
          Version: '2020-11-09',
          InputURL: params.mediaURL,
          Title: params.title || '',
          Description: params.description || '',
          Tags: params.tags || '',
          Category: params.category || ''
        }
      });

      ElMessage.success('媒资注册成功');
      return response.data;
    } catch (error) {
      ElMessage.error(`媒资注册失败: ${error instanceof Error ? error.message : '未知错误'}`);
      throw error;
    }
  }

  /**
   * 更新媒资信息
   */
  async updateMediaInfo(params: {
    mediaId: string;
    title?: string;
    description?: string;
    tags?: string;
    category?: string;
    coverURL?: string;
  }): Promise<boolean> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const requestData: any = {
        Action: 'UpdateMediaInfo',
        Version: '2020-11-09',
        MediaId: params.mediaId
      };

      if (params.title !== undefined) requestData.Title = params.title;
      if (params.description !== undefined) requestData.Description = params.description;
      if (params.tags !== undefined) requestData.Tags = params.tags;
      if (params.category !== undefined) requestData.Category = params.category;
      if (params.coverURL !== undefined) requestData.CoverURL = params.coverURL;

      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: requestData
      });

      ElMessage.success('媒资信息更新成功');
      return (response as any).success !== false;
    } catch (error) {
      ElMessage.error(`媒资信息更新失败: ${error instanceof Error ? error.message : '未知错误'}`);
      return false;
    }
  }

  /**
   * 删除媒资
   */
  async deleteMediaInfo(mediaId: string): Promise<boolean> {
    if (!this.isReady()) {
      throw new Error('ICE客户端未初始化');
    }

    try {
      const response = await request({
        url: '/open/openApiPost',
        method: 'post',
        data: {
          Action: 'DeleteMediaInfo',
          Version: '2020-11-09',
          MediaId: mediaId
        }
      });

      ElMessage.success('媒资删除成功');
      return (response as any).success !== false;
    } catch (error) {
      ElMessage.error(`媒资删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
      return false;
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.isInitialized = false;
    this.config = null;
  }
}

// 导出单例实例
export const iceNPMManager = new ICENPMManager();

export default iceNPMManager;