<template>
  <div class="image-library">
    <!-- 操作栏 -->
    <div class="toolbar">
      <div class="left-actions">
        <el-button type="primary" @click="showUploadDialog">
          <el-icon><Plus /></el-icon>
          上传图片
        </el-button>
        <el-button @click="batchDelete" :disabled="selectedImages.length === 0">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      <div class="right-actions">
        <!-- 筛选业务类型 -->
        <el-select v-model="businessType" placeholder="业务类型" style="width: 120px; margin-right: 10px;">
          <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <!-- 筛选来源 -->
        <el-select v-model="source" placeholder="来源" style="width: 120px; margin-right: 10px;">
          <el-option v-for="item in sourceOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <!-- 筛选资源状态 -->
        <el-select v-model="status" placeholder="资源状态" style="width: 120px; margin-right: 10px;">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <!-- 日期筛选 -->
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px; margin-right: 10px;"
          @change="handleDateChange"
        />
        <!-- 排序 -->
        <el-select v-model="sortBy" placeholder="排序" style="width: 120px; margin-right: 10px;">
          <el-option label="升序" value="utcCreate:Asc" />
          <el-option label="降序" value="utcCreate:Desc" />
        </el-select>
        <!-- 搜索框 -->
        <el-input
          v-model="searchKeyword"
          placeholder="搜索图片..."
          style="width: 200px; margin-right: 10px;"
          @input="handleSearchInput"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="handleFilter">筛选</el-button>
      </div>
    </div>

    <!-- 分类筛选 -->
    <div class="category-filter">
      <el-button-group>
        <el-button 
          :type="selectedCategory === 'all' ? 'primary' : 'default'"
          @click="filterByCategory('all')"
        >
          全部
        </el-button>
        <el-button 
          :type="selectedCategory === 'photo' ? 'primary' : 'default'"
          @click="filterByCategory('photo')"
        >
          照片
        </el-button>
        <el-button 
          :type="selectedCategory === 'illustration' ? 'primary' : 'default'"
          @click="filterByCategory('illustration')"
        >
          插画
        </el-button>
        <el-button 
          :type="selectedCategory === 'icon' ? 'primary' : 'default'"
          @click="filterByCategory('icon')"
        >
          图标
        </el-button>
        <el-button 
          :type="selectedCategory === 'background' ? 'primary' : 'default'"
          @click="filterByCategory('background')"
        >
          背景图
        </el-button>
      </el-button-group>
    </div>

    <!-- 图片网格展示 -->
    <div class="image-grid" v-loading="props.loading">
      <div
        v-for="image in filteredImages"
        :key="image.MediaId"
        class="image-card"
        @click="selectImage(image)"
        :class="{ selected: selectedImages.includes(image.MediaId) }"
      >
        <div class="image-container">
          <img
            :src="getImageUrl(image)"
            :alt="image.MediaBasicInfo?.Title || '图片'"
            @error="handleImageError"
          />
          <div class="image-overlay">
            <el-button
              type="primary"
              size="small"
              @click.stop="previewImage(image)"
            >
              <el-icon><View /></el-icon>
              预览
            </el-button>
            <el-button
              type="success"
              size="small"
              @click.stop="downloadImage(image)"
            >
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click.stop="deleteImage(image.MediaId)"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
          <!-- 格式标识 -->
          <div class="format-badge">
            {{ getImageFormat(image.FileInfoList?.[0]?.FileBasicInfo?.FileName) }}
          </div>
        </div>
        <div class="image-info">
          <div class="image-name" :title="image.MediaBasicInfo?.Title || '未命名图片'">
            {{ image.MediaBasicInfo?.Title || '未命名图片' }}
          </div>
          <div class="image-meta">
            <span class="resolution" v-if="image.FileInfoList?.[0]?.FileBasicInfo?.Width">
              {{ image.FileInfoList[0].FileBasicInfo.Width }}×{{ image.FileInfoList[0].FileBasicInfo.Height }}
            </span>
            <span class="size">{{ formatFileSize(image.FileInfoList?.[0]?.FileBasicInfo?.FileSize) }}</span>
            <span class="date">{{ formatDate(image.CreationTime) }}</span>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!props.loading && filteredImages.length === 0" class="empty-state">
        <el-empty description="暂无图片素材">
          <el-button type="primary" @click="showUploadDialog">上传图片</el-button>
        </el-empty>
      </div>
    </div>

    <!-- 上传组件 -->
    <MediaUploader
      v-model:visible="uploadDialogVisible"
      media-type="image"
      @upload-success="handleUploadSuccess"
      @upload-error="handleUploadError"
    />

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewDialogVisible" title="图片预览" width="80%" @close="closePreview">
      <div class="image-preview" v-if="previewImageData">
        <div class="preview-image-container">
          <img :src="previewImageData.url" :alt="previewImageData.name" />
        </div>
        <div class="preview-info">
          <h3>{{ previewImageData.name }}</h3>
          <div class="info-grid">
            <p><strong>分辨率:</strong> {{ previewImageData.width }}×{{ previewImageData.height }}</p>
            <p><strong>文件大小:</strong> {{ formatFileSize(previewImageData.size) }}</p>
            <p><strong>格式:</strong> {{ previewImageData.format }}</p>
            <p><strong>上传时间:</strong> {{ formatDateTime(previewImageData.uploadTime) }}</p>
            <p v-if="previewImageData.description"><strong>描述:</strong> {{ previewImageData.description }}</p>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="preview-footer">
          <el-button @click="closePreview">关闭</el-button>
          <el-button type="primary" @click="downloadCurrentImage">下载</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Search, View, Download, Refresh } from '@element-plus/icons-vue'
import MediaUploader from './add/MediaUploader.vue'
import { formatFileSize } from '../../utils/commonUtils'
import { BusinessTypeMap, StatusMap, SourceMap, mapToOptions } from '../../types/media'
import type { MediaInfo } from '../../types/media'

// 定义组件的事件
const emit = defineEmits<{
  upload: [uploadParams: { file: File; MediaMetaData?: any }],
  sizeChange: [size: number],
  pageChange: [current: number],
  search: [keyword: string],
  filter: [params: any],
  refreshMediaList: [page?: number]
}>()

// 定义props
const props = defineProps<{
  mediaList?: MediaInfo[],
  currentPage?: number,
  pageSize?: number,
  total?: number,
  loading?: boolean
}>()

// 数据定义
const searchKeyword = ref('')
const selectedCategory = ref('all')
const selectedImages = ref<string[]>([])
const currentPreviewImage = ref<MediaInfo | null>(null)

// 筛选相关变量
const businessType = ref('')
const source = ref('')
const status = ref('')
const dateRange = ref<[Date, Date] | null>(null)
const sortBy = ref<string>('utcCreate:Desc')
const startTime = ref<string>('')
const endTime = ref<string>('')

// 下拉选项
const businessTypeOptions = mapToOptions(BusinessTypeMap)
const sourceOptions = mapToOptions(SourceMap)
const statusOptions = mapToOptions(StatusMap)

// 对话框控制
const uploadDialogVisible = ref(false)
const previewDialogVisible = ref(false)
const previewImageData = ref<any>(null)

// 计算属性
const filteredImages = computed(() => {
  return props.mediaList || []
})

// 获取图片URL
const getImageUrl = (image: MediaInfo): string => {
  return image.MediaBasicInfo?.CoverURL || 
         image.FileInfoList?.[0]?.FileBasicInfo?.FileUrl || 
         '/default-image.png'
}

// 日期筛选处理
const handleDateChange = (val: [Date, Date] | null) => {
  if (val && val.length === 2) {
    startTime.value = val[0].toISOString()
    endTime.value = val[1].toISOString()
  } else {
    startTime.value = ''
    endTime.value = ''
  }
}

// 搜索处理
const handleSearchInput = () => {
  emit('search', searchKeyword.value)
}

// 筛选按钮处理
const handleFilter = () => {
  emit('filter', {
    businessType: businessType.value,
    source: source.value,
    status: status.value,
    startTime: startTime.value,
    endTime: endTime.value,
    sortBy: sortBy.value,
    searchKeyword: searchKeyword.value
  })
}

// 方法定义
const showUploadDialog = () => {
  uploadDialogVisible.value = true
}

const handleRefresh = () => {
  emit('refreshMediaList')
}

const selectImage = (image: MediaInfo) => {
  const index = selectedImages.value.indexOf(image.MediaId)
  if (index > -1) {
    selectedImages.value.splice(index, 1)
  } else {
    selectedImages.value.push(image.MediaId)
  }
}

const previewImage = (image: MediaInfo) => {
  currentPreviewImage.value = image
  previewImageData.value = {
    url: getImageUrl(image),
    name: image.MediaBasicInfo?.Title || '未命名图片',
    width: image.FileInfoList?.[0]?.FileBasicInfo?.Width || '未知',
    height: image.FileInfoList?.[0]?.FileBasicInfo?.Height || '未知',
    size: image.FileInfoList?.[0]?.FileBasicInfo?.FileSize || 0,
    format: getImageFormat(image.FileInfoList?.[0]?.FileBasicInfo?.FileName),
    uploadTime: image.CreationTime || image.MediaBasicInfo?.CreateTime,
    description: image.MediaBasicInfo?.Description
  }
  previewDialogVisible.value = true
}

const closePreview = () => {
  previewDialogVisible.value = false
  previewImageData.value = null
  currentPreviewImage.value = null
}

const downloadCurrentImage = () => {
  if (currentPreviewImage.value) {
    downloadImage(currentPreviewImage.value)
  }
}

const deleteImage = async (imageId: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这张图片吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    ElMessage.success('删除成功')
    emit('refreshMediaList')
  } catch {
    // 用户取消删除
  }
}

const batchDelete = async () => {
  if (selectedImages.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedImages.value.length} 张图片吗？删除后无法恢复。`,
      '批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    ElMessage.success('批量删除成功')
    selectedImages.value = []
    emit('refreshMediaList')
  } catch {
    // 用户取消删除
  }
}

const filterByCategory = (category: string) => {
  selectedCategory.value = category
  // 这里可以根据分类进行筛选，暂时保留UI交互
}

// 分页事件 emit 给父组件
const handleSizeChange = (size: number) => {
  emit('sizeChange', size)
}

const handleCurrentChange = (current: number) => {
  emit('pageChange', current)
}

const handleUploadSuccess = (result: any) => {
  ElMessage.success('图片上传成功！')
  uploadDialogVisible.value = false
  emit('refreshMediaList')
}

const handleUploadError = (error: any) => {
  ElMessage.error('图片上传失败: ' + (error.message || '未知错误'))
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/default-image.png'
}

const getImageFormat = (fileName?: string) => {
  if (!fileName) return ''
  const ext = fileName.split('.').pop()?.toUpperCase()
  return ext || ''
}

const downloadImage = async (image: MediaInfo) => {
  try {
    const url = getImageUrl(image)
    if (!url || url === '/default-image.png') {
      ElMessage.error('无法获取图片下载链接')
      return
    }

    const link = document.createElement('a')
    link.href = url
    link.download = image.MediaBasicInfo?.Title || 'image'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('图片下载成功')
  } catch (error) {
    console.error('下载图片失败:', error)
    ElMessage.error('下载图片失败')
  }
}

// 监听上传成功事件
const handleMediaUploaded = (event: Event) => {
  const customEvent = event as CustomEvent
  const { category } = customEvent.detail
  if (category === 'image') {
    emit('refreshMediaList')
  }
}

onMounted(() => {
  // 监听上传成功事件
  window.addEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})
</script>

<style scoped>
.image-library {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 18px 22px;
  background: #f7fafd;
  border-radius: 14px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  gap: 18px;

  .left-actions {
    display: flex;
    gap: 14px;

    .el-button {
      border-radius: 7px;
      font-size: 15px;
      font-weight: 500;
      padding: 0 18px;
      height: 38px;

      &.el-button--primary {
        background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
        color: #fff;
        border: none;
        box-shadow: 0 2px 8px rgba(64,158,255,0.08);
      }

      &.el-button--danger {
        background: #fff0f0;
        color: #f56c6c;
        border: 1px solid #fbc4c4;
      }

      &:disabled {
        background: #f5f7fa !important;
        color: #bfbfbf !important;
        border: 1px solid #e4e7ed !important;
      }

      .el-icon {
        margin-right: 6px;
      }
    }
  }

  .right-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    .el-select, .el-date-picker, .el-input {
      border-radius: 7px;
      font-size: 15px;
      height: 38px;

      :deep(.el-input__wrapper), :deep(.el-input__inner) {
        border-radius: 7px;
        height: 38px;
        font-size: 15px;
      }
    }

    .el-button {
      border-radius: 7px;
      font-size: 15px;
      padding: 0 18px;
      height: 38px;

      &.el-button--primary {
        background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
        color: #fff;
        border: none;
      }
    }
  }
}

.category-filter {
  padding: 0 16px 16px 16px;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;
}

.image-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 16px;
  overflow-y: auto;
  padding: 0 4px 16px 0;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.image-card {
  border: 2px solid transparent;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(.4,0,.2,1);
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);

  &:hover {
    border-color: #409eff;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 6px 18px rgba(64,158,255,0.15);
  }

  &.selected {
    border-color: #409eff;
    background: #ecf5ff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
}

.image-container {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s;
  }

  &:hover .image-overlay {
    opacity: 1;
  }

  .format-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }
}

.image-info {
  padding: 12px;
}

.image-name {
  font-weight: 500;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #303133;
}

.image-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  gap: 8px;

  span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
  }
}

.empty-state {
  grid-column: 1 / -1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.image-preview {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.preview-image-container {
  text-align: center;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
  
  img {
    max-width: 100%;
    max-height: 60vh;
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.preview-info {
  padding: 16px;
  background: #f9fafc;
  border-radius: 8px;

  h3 {
    margin: 0 0 16px 0;
    color: #303133;
    font-size: 18px;
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;

      strong {
        color: #303133;
        margin-right: 8px;
      }
    }
  }
}

.preview-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
