<template>
  <div class="voice-step">
    <div class="panel-header">
      <div class="header-info">
        <h3 class="panel-title">
          <el-icon class="panel-icon"><Microphone /></el-icon>
          选择数字人声音
        </h3>
        <p class="panel-desc">为每个数字人选择合适的声音，需要选择 {{ avatarCount }} 个声音</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="goToSelectVoices" class="select-btn">
          <el-icon><Plus /></el-icon>选择声音
        </el-button>
      </div>
    </div>

    <div class="voice-content">
      <div class="selection-status">
        <span class="status-text">已选择 {{ internalSelectedVoices.length }}/{{ avatarCount }} 个声音</span>
        <div class="status-bar">
          <div class="status-progress" :style="{ width: `${avatarCount > 0 ? (internalSelectedVoices.length / avatarCount) * 100 : 0}%` }"></div>
        </div>
      </div>

      <div v-if="internalSelectedVoices.length === 0" class="empty-state">
        <div class="empty-icon">
          <el-icon><Microphone /></el-icon>
        </div>
        <h4 class="empty-title">暂未选择任何声音</h4>
        <p class="empty-desc">请为每个数字人选择一个声音</p>
        <el-button type="primary" @click="goToSelectVoices" class="empty-action">
          立即选择
        </el-button>
      </div>

      <div v-else class="voice-grid">
        <div
          v-for="(voice, index) in internalSelectedVoices"
          :key="voice.soundId"
          class="voice-card"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <div class="card-preview">
            <div class="voice-avatar">
              <el-icon><Microphone /></el-icon>
            </div>
            <div class="card-overlay">
              <el-button 
                type="danger" 
                size="small" 
                circle 
                @click="removeVoice(voice.soundId)"
                class="remove-btn"
              >
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
          </div>
          <div class="card-info">
            <h5 class="voice-name">{{ voice.soundName }}</h5>
            <audio v-if="voice.soundRef" :src="voice.soundRef" controls class="voice-player" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Microphone, Plus, Close } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()

const props = defineProps({
  selectedVoices: {
    type: Array,
    default: () => []
  },
  avatarCount: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['update-voices'])

// 组件内部状态
const internalSelectedVoices = ref([...props.selectedVoices])

// 内置音色列表
const builtinVoices = ['zhiyuan', 'zhiyue', 'zhisha', 'zhida', 'aiqi', 'aicheng', 'aijia', 'siqi', 'sijia', 'mashu', 'yueer', 'ruoxi', 'aida', 'sicheng', 'ninger', 'xiaoyun', 'xiaogang', 'ruilin']

// 处理声音数据格式化
const formatVoiceData = (item) => {
  if (item.audioId) {
    // 用户上传的音频（系统声音）
    return {
      soundId: item.audioId.toString(),
      soundName: item.content,
      soundRef: item.audioPath || '',
      createBy: 'user',
      voiceType: 'system',
      createTime: item.createTime || new Date().toISOString()
    }
  } else {
    // 内置音色或其他类型
    const soundId = item.soundId || item.value
    const soundName = item.soundName || item.label
    const isBuiltin = builtinVoices.includes(soundId) || builtinVoices.includes(soundName)

    return {
      soundId: soundId,
      soundName: soundName,
      soundRef: item.soundRef || '',
      createBy: isBuiltin ? 'admin' : 'user',
      voiceType: isBuiltin ? 'builtin' : 'system',
      createTime: item.createTime || new Date().toISOString()
    }
  }
}

const goToSelectVoices = () => {
  if (props.avatarCount === 0) {
    ElMessage.warning('请先选择数字人形象')
    return
  }
  router.push({
    path: '/szbSound/textAudio',
    query: {
      from: 'dialogueSynthesis',
      mode: 'select',
      returnPath: '/szbVideo/dialogueSynthesis',
      maxCount: props.avatarCount
    }
  })
}

const removeVoice = (soundId) => {
  const voice = internalSelectedVoices.value.find(v => v.soundId === soundId)
  if (!voice) return

  ElMessageBox.confirm(
    `确定要移除声音 "${voice.soundName}" 吗？`,
    '确认移除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = internalSelectedVoices.value.findIndex(v => v.soundId === soundId)
    if (index > -1) {
      internalSelectedVoices.value.splice(index, 1)
      emit('update-voices', internalSelectedVoices.value)
      const remaining = props.avatarCount - internalSelectedVoices.value.length
      if (remaining > 0) {
        ElMessage.warning(`还需要选择 ${remaining} 个声音`)
      }
    }
  }).catch(() => {})
}

watch(() => props.selectedVoices, (newVoices) => {
  internalSelectedVoices.value = [...newVoices]
}, { immediate: true })
</script>

<style lang="scss" scoped>
.voice-step {
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    border: 1px solid #e9ecef;

    .header-info {
      .panel-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin: 0 0 8px 0;

        .panel-icon {
          font-size: 28px;
          color: #667eea;
        }
      }

      .panel-desc {
        font-size: 16px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .select-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
      }
    }
  }

  .selection-status {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 1px solid #e9ecef;

    .status-text {
      font-weight: 600;
      color: #333;
      font-size: 16px;
    }

    .status-bar {
      flex: 1;
      height: 8px;
      background: #e9ecef;
      border-radius: 4px;
      overflow: hidden;

      .status-progress {
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 4px;
        transition: width 0.6s ease;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    border: 2px dashed #dee2e6;

    .empty-icon {
      font-size: 64px;
      color: #dee2e6;
      margin-bottom: 20px;
    }

    .empty-title {
      font-size: 20px;
      font-weight: 600;
      color: #666;
      margin: 0 0 12px 0;
    }

    .empty-desc {
      font-size: 16px;
      color: #999;
      margin: 0 0 24px 0;
      line-height: 1.5;
    }

    .empty-action {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 12px;
      padding: 12px 32px;
      font-weight: 600;
      font-size: 16px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
      }
    }
  }

  .voice-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
  }

  .voice-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
    }

    .card-preview {
      position: relative;
      height: 140px;
      overflow: hidden;

      .voice-avatar {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-size: 48px;
      }

      .card-overlay {
        position: absolute;
        top: 12px;
        right: 12px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover .card-overlay {
        opacity: 1;
      }

      .remove-btn {
        background: rgba(245, 108, 108, 0.9);
        border: none;
        color: white;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;

        &:hover {
          background: rgba(245, 108, 108, 1);
          transform: scale(1.1);
        }
      }
    }

    .card-info {
      padding: 16px;

      .voice-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 12px 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .voice-player {
        width: 100%;
        height: 32px;
        border-radius: 8px;
      }
    }
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
