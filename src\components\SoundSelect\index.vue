<template>
  <div class="sound-select">
    <el-popover
      v-model:visible="popoverVisible"
      placement="bottom-start"
      :width="300"
      trigger="click"
      popper-class="sound-select-popover"
    >
      <template #reference>
        <div class="select-trigger" :class="{ 'is-active': popoverVisible }">
          <span class="trigger-text">{{ selectedLabel }}</span>
          <el-icon class="trigger-icon" :class="{ 'is-reverse': popoverVisible }">
            <ArrowDown />
          </el-icon>
        </div>
      </template>
      
      <div class="sound-list">
        <div class="sound-list__search">
          <el-input
            v-model="searchText"
            placeholder="搜索声音"
            clearable
            prefix-icon="Search"
          />
        </div>
        
        <div class="sound-list__content">
          <template v-if="filteredSounds.length">
            <div
              v-for="sound in filteredSounds"
              :key="sound.value"
              class="sound-item"
              :class="{ 'is-active': modelValue === sound.value }"
              :data-type="sound.createBy === 'admin' ? 'built-in' : 'custom'"
              @click="handleSelect(sound)"
            >
              <div class="sound-item__icon">
                <el-icon><Mic /></el-icon>
              </div>
              <div class="sound-item__info">
                <div class="sound-item__name">{{ sound.label }}</div>
                <div class="sound-item__desc">{{ sound.createBy === 'admin' ? '内置音色' : '定制音色' }}</div>
              </div>
              <div class="sound-item__check" v-if="modelValue === sound.value">
                <el-icon><Check /></el-icon>
              </div>
            </div>
          </template>
          <el-empty v-else description="未找到匹配的声音" />
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { listSound } from '@/api/platform/sound'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

const popoverVisible = ref(false)
const searchText = ref('')
const sounds = ref([])

// 获取声音列表
const getSounds = async () => {
  try {
    const res = await listSound({ soundStatus: '2' })
    if (res.total > 0) {
      sounds.value = res.rows.map(item => ({
        label: item.soundName,
        value: item.soundId,
        description: item.description,
        createBy: item.createBy
      }))
    }
  } catch (error) {
    console.error(error)
  }
}

// 过滤后的声音列表
const filteredSounds = computed(() => {
  if (!searchText.value) return sounds.value
  
  const search = searchText.value.toLowerCase()
  return sounds.value.filter(sound => 
    sound.label.toLowerCase().includes(search) ||
    (sound.description && sound.description.toLowerCase().includes(search))
  )
})

// 选中的声音标签
const selectedLabel = computed(() => {
  const sound = sounds.value.find(s => s.value === props.modelValue)
  return sound?.label
})

// 选择声音
const handleSelect = (sound) => {
  emit('update:modelValue', sound.value)
  popoverVisible.value = false
}

// 初始化
onMounted(() => {
  getSounds()
})
</script>

<style lang="scss" scoped>
.sound-select {
  display: inline-block;
  position: relative;
}

.select-trigger {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 0 12px;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 160px;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  }
  
  &.is-active {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
  
  .trigger-text {
    flex: 1;
    font-size: 14px;
    color: #606266;
    margin-right: 8px;
    
    &:empty::before {
      content: '选择声音';
      color: #909399;
    }
  }
  
  .trigger-icon {
    font-size: 12px;
    color: #909399;
    transition: transform 0.3s ease;
    
    &.is-reverse {
      transform: rotate(180deg);
    }
  }
}

:deep(.sound-select-popover) {
  padding: 0;
  border-radius: 12px;
  box-shadow: 
    0 6px 16px -8px rgba(0, 0, 0, 0.08),
    0 9px 28px 0 rgba(0, 0, 0, 0.05),
    0 12px 48px 16px rgba(0, 0, 0, 0.03);
  border: none;
}

.sound-list {
  &__search {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    
    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 1px #dcdfe6;
      
      &:hover {
        box-shadow: 0 0 0 1px #c0c4cc;
      }
      
      &.is-focus {
        box-shadow: 0 0 0 1px #409eff;
      }
    }
  }
  
  &__content {
    max-height: 300px;
    overflow-y: auto;
    padding: 8px 0;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #dcdfe6;
      border-radius: 3px;
      
      &:hover {
        background: #c0c4cc;
      }
    }
  }
}

.sound-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    background: #f5f7fa;
    
    .sound-item__icon {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
  
  &.is-active {
    background: #ecf5ff;
    
    .sound-item__icon {
      background: linear-gradient(135deg, #409eff, #2d6feb);
      
      .el-icon {
        color: #fff;
      }
    }
    
    .sound-item__name {
      color: #409eff;
      font-weight: 500;
    }
  }
  
  &__icon {
    width: 36px;
    height: 36px;
    border-radius: 10px;
    background: #f4f4f5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    
    .el-icon {
      font-size: 18px;
      color: #909399;
      transition: all 0.3s ease;
    }
  }
  
  &__info {
    flex: 1;
    min-width: 0;
  }
  
  &__name {
    font-size: 14px;
    color: #606266;
    margin-bottom: 4px;
    transition: all 0.3s ease;
  }
  
  &__desc {
    font-size: 12px;
    color: #909399;
    display: flex;
    align-items: center;
    
    &::before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-right: 6px;
    }
  }
  
  // 内置音色样式
  &[data-type="built-in"] {
    .sound-item__icon {
      background: linear-gradient(135deg, #67c23a, #4e9e2a);
      
      .el-icon {
        color: #fff;
      }
    }
    
    .sound-item__desc::before {
      background: #67c23a;
    }
  }
  
  // 定制音色样式
  &[data-type="custom"] {
    .sound-item__icon {
      background: linear-gradient(135deg, #e6a23c, #cf8f2e);
      
      .el-icon {
        color: #fff;
      }
    }
    
    .sound-item__desc::before {
      background: #e6a23c;
    }
  }
  
  &__check {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #409eff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
    flex-shrink: 0;

    .el-icon {
      font-size: 12px;
      color: #fff;
    }
  }
}
</style> 